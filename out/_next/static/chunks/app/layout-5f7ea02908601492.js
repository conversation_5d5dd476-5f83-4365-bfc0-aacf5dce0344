(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1654:(e,t,a)=>{"use strict";a.d(t,{Layout:()=>w});var o=a(5155),n=a(2115),r=a(6001),i=a(760),s=a(4416);let l=(0,a(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var d=a(9434),c=a(1950);let p=(0,n.createContext)(void 0);function x(e){let{children:t,defaultTheme:a=c.B2.defaultTheme}=e,[r,i]=(0,n.useState)(a),[s,l]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{let e=localStorage.getItem(c.B2.storageKey);e&&("light"===e||"dark"===e)?i(e):i(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),l(!0)},[]),(0,n.useEffect)(()=>{if(!s)return;let e=document.documentElement;e.classList.remove("light","dark"),e.classList.add(r),localStorage.setItem(c.B2.storageKey,r),document.body.classList.toggle("dark-theme","dark"===r)},[r,s]),s)?(0,o.jsx)(p.Provider,{value:{theme:r,toggleTheme:()=>{document.body.classList.add("dark-mode-animation"),i(e=>"light"===e?"dark":"light"),setTimeout(()=>{document.body.classList.remove("dark-mode-animation")},1e3)},setTheme:i},children:t}):(0,o.jsx)("div",{className:"min-h-screen bg-background text-foreground",children:t})}function u(e){let{className:t}=e,a=(0,n.useContext)(p),i=(null==a?void 0:a.theme)||"light",s=(null==a?void 0:a.toggleTheme)||(()=>{}),l="dark"===i;return(0,o.jsxs)("div",{className:(0,d.cn)("switch relative",t),children:[(0,o.jsx)("input",{type:"checkbox",id:"toggle",checked:l,onChange:s,className:"absolute left-0 top-0 h-full w-full opacity-0 z-[100] cursor-pointer"}),(0,o.jsx)("label",{htmlFor:"toggle",className:"block h-[60px] w-[60px] bg-white rounded-full shadow-[inset_0_0_20px_rgba(0,0,0,0.2),inset_0_0_5px_-2px_rgba(0,0,0,0.4)] dark:bg-[var(--dark-theme-background)]",children:(0,o.jsxs)(r.P.i,{className:(0,d.cn)("bulb block relative h-[50px] w-[50px] rounded-full top-[5px] left-[5px] transition-all duration-[0.9s]",l?"bg-[#a7694a] shadow-[inset_0_0_1px_3px_#a56758,inset_0_0_6px_8px_#6b454f,0_20px_30px_-10px_rgba(0,0,0,0.4),0_0_30px_50px_rgba(253,184,67,0.1)]":"bg-[#2d2e32] shadow-[inset_0_0_1px_3px_#2d2e32,inset_0_0_6px_8px_#1e1e20,0_20px_30px_-10px_rgba(0,0,0,0.2)]"),animate:{backgroundColor:l?"#a7694a":"#2d2e32"},transition:{duration:.9},children:[(0,o.jsx)(r.P.span,{className:(0,d.cn)("bulb-center absolute block h-[36px] w-[36px] rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-[0.7s]",l?"bg-[#feed6b] shadow-[inset_0_0_0_4px_#fdec6a,0_0_12px_10px_#bca83c,0_0_20px_14px_#a1664a]":"bg-[#3a3a3c] shadow-[inset_0_0_0_4px_#444]"),animate:{backgroundColor:l?"#feed6b":"#3a3a3c"},transition:{duration:.7},children:(0,o.jsx)(r.P.span,{className:(0,d.cn)("absolute block h-[20px] w-[20px] rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-[0.7s]",l?"bg-[#fef401] shadow-[0_0_2px_4px_#fdb843]":"bg-[#464648] shadow-[inset_0_0_0_2px_#2a2a2c]"),animate:{backgroundColor:l?"#fef401":"#464648"},transition:{duration:.7}})}),(0,o.jsxs)(r.P.span,{className:"filament-1 absolute block h-[35px] w-[35px] rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rotate-[-45deg] overflow-hidden",children:[(0,o.jsx)(r.P.span,{className:"absolute block h-[6px] w-[17px] rounded-full border-2 top-[-4px] left-[-2px] transform rotate-[-10deg]",style:{borderColor:l?"#fef4d5":"#4a426b"},animate:{borderColor:l?"#fef4d5":"#4a426b"},transition:{duration:.7}}),(0,o.jsx)(r.P.span,{className:"absolute block h-[6px] w-[17px] rounded-full border-2 top-[-4px] left-[15px] transform rotate-[10deg]",style:{borderColor:l?"#fef4d5":"#4a426b"},animate:{borderColor:l?"#fef4d5":"#4a426b"},transition:{duration:.7}})]}),(0,o.jsxs)(r.P.span,{className:"filament-2 absolute block h-[35px] w-[35px] rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rotate-[45deg] overflow-hidden",children:[(0,o.jsx)(r.P.span,{className:"absolute block h-[6px] w-[17px] rounded-full border-2 top-[-4px] left-[-2px] transform rotate-[-10deg]",style:{borderColor:l?"#fef4d5":"#4a426b"},animate:{borderColor:l?"#fef4d5":"#4a426b"},transition:{duration:.7}}),(0,o.jsx)(r.P.span,{className:"absolute block h-[6px] w-[17px] rounded-full border-2 top-[-4px] left-[15px] transform rotate-[10deg]",style:{borderColor:l?"#fef4d5":"#4a426b"},animate:{borderColor:l?"#fef4d5":"#4a426b"},transition:{duration:.7}})]}),(0,o.jsx)(r.P.span,{className:(0,d.cn)("reflections absolute block h-[12px] w-[12px] top-[8px] left-[8px] rounded-full transition-all duration-[0.7s]",l?"bg-[rgba(255,255,255,0.2)]":"bg-[rgba(255,255,255,0.1)]"),animate:{opacity:l?1:.8},transition:{duration:.7},children:(0,o.jsx)(r.P.span,{className:(0,d.cn)("absolute block h-[6px] w-[6px] top-[2px] left-[2px] rounded-full transition-all duration-[0.7s]",l?"bg-[rgba(255,255,255,0.4)]":"bg-[rgba(255,255,255,0.2)]"),animate:{opacity:l?1:.6},transition:{duration:.7}})}),(0,o.jsxs)(r.P.span,{className:"sparks absolute block h-[50px] w-[50px] top-0 left-0",animate:{opacity:+!!l},transition:{duration:.4},children:[(0,o.jsx)(r.P.i,{className:"spark1 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{right:"-5px",bottom:"23px"},animate:l?{opacity:[0,0,1,1,0],right:["-5px","-5px","0px","0px","-70px"],bottom:["23px","23px","23px","23px","50px"]}:{opacity:0},transition:{duration:20,delay:1.2,repeat:1/0,ease:"linear",times:[0,.4,.5,.9,1]}}),(0,o.jsx)(r.P.i,{className:"spark2 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{right:"20px",bottom:"80px"},animate:l?{opacity:[0,0,1,1,0],right:["20px","20px","20px","20px","10px"],bottom:["80px","80px","80px","80px","90px"]}:{opacity:0},transition:{duration:18,delay:2,repeat:1/0,ease:"linear",times:[0,.5,.6,.9,1]}}),(0,o.jsx)(r.P.i,{className:"spark3 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{left:"20px",bottom:"80px"},animate:l?{opacity:[0,0,1,1,0],left:["20px","20px","20px","20px","-30px"],bottom:["80px","80px","80px","80px","60px"]}:{opacity:0},transition:{duration:16,delay:3,repeat:1/0,ease:"linear",times:[0,.5,.6,.9,1]}}),(0,o.jsx)(r.P.i,{className:"spark4 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{left:"20px",bottom:"20px"},animate:l?{opacity:[0,0,1,1,0],left:["20px","20px","20px","20px","50px"],bottom:["20px","20px","20px","20px","30px"]}:{opacity:0},transition:{duration:14.5,delay:3.5,repeat:1/0,ease:"linear",times:[0,.5,.6,.9,1]}}),(0,o.jsx)(r.P.i,{className:"spark5 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{right:"10px",top:"10px"},animate:l?{opacity:[0,0,1,1,0],right:["10px","10px","10px","10px","60px"],top:["10px","10px","10px","10px","50px"]}:{opacity:0},transition:{duration:13,delay:1.8,repeat:1/0,ease:"linear",times:[0,.5,.6,.9,1]}}),(0,o.jsx)(r.P.i,{className:"spark6 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{left:"10px",top:"10px"},animate:l?{opacity:[0,0,1,1,0],left:["10px","10px","10px","10px","40px"],top:["10px","10px","10px","10px","70px"]}:{opacity:0},transition:{duration:17.5,delay:2.5,repeat:1/0,ease:"linear",times:[0,.5,.6,.9,1]}}),(0,o.jsx)(r.P.i,{className:"spark7 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{right:"30px",bottom:"10px"},animate:l?{opacity:[0,0,1,1,0],right:["30px","30px","30px","30px","80px"],bottom:["10px","10px","10px","10px","-10px"]}:{opacity:0},transition:{duration:15.2,delay:2.2,repeat:1/0,ease:"linear",times:[0,.5,.6,.9,1]}}),(0,o.jsx)(r.P.i,{className:"spark8 absolute block h-[8px] w-[8px] bg-[#d1b82b] rounded-full shadow-[0_0_12px_#d1b82b,0_0_18px_#fdb843]",style:{left:"30px",bottom:"10px"},animate:l?{opacity:[0,0,1,1,0],left:["30px","30px","30px","30px","70px"],bottom:["10px","10px","10px","10px","0px"]}:{opacity:0},transition:{duration:19,delay:4,repeat:1/0,ease:"linear",times:[0,.5,.6,.9,1]}})]})]})})]})}function m(e){let{className:t}=e,a=(0,n.useContext)(p),i=(null==a?void 0:a.theme)||"light",s="dark"===i;return(0,o.jsx)(r.P.svg,{viewBox:"0 0 100 100",className:(0,d.cn)("logo-svg",t),style:{stroke:s?"#fff":"#000"},initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.5},children:(0,o.jsxs)("g",{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"6",children:[(0,o.jsx)("path",{d:"M 21 40 V 59",children:(0,o.jsx)("animateTransform",{attributeName:"transform",attributeType:"XML",type:"rotate",values:"0 21 59; 180 21 59",dur:"2s",repeatCount:"indefinite"})}),(0,o.jsx)("path",{d:"M 79 40 V 59",children:(0,o.jsx)("animateTransform",{attributeName:"transform",attributeType:"XML",type:"rotate",values:"0 79 59; -180 79 59",dur:"2s",repeatCount:"indefinite"})}),(0,o.jsx)("path",{d:"M 50 21 V 40",children:(0,o.jsx)("animate",{attributeName:"d",values:"M 50 21 V 40; M 50 59 V 40",dur:"2s",repeatCount:"indefinite"})}),(0,o.jsx)("path",{d:"M 50 60 V 79",children:(0,o.jsx)("animate",{attributeName:"d",values:"M 50 60 V 79; M 50 98 V 79",dur:"2s",repeatCount:"indefinite"})}),(0,o.jsx)("path",{d:"M 50 21 L 79 40 L 50 60 L 21 40 Z",children:(0,o.jsx)("animate",{attributeName:"stroke",values:s?"rgba(255,255,255,1); rgba(100,100,100,0)":"rgba(0,0,0,1); rgba(100,100,100,0)",dur:"2s",repeatCount:"indefinite"})}),(0,o.jsx)("path",{d:"M 50 40 L 79 59 L 50 79 L 21 59 Z"}),(0,o.jsx)("path",{d:"M 50 59 L 79 78 L 50 98 L 21 78 Z",children:(0,o.jsx)("animate",{attributeName:"stroke",values:s?"rgba(100,100,100,0); rgba(255,255,255,1)":"rgba(100,100,100,0); rgba(0,0,0,1)",dur:"2s",repeatCount:"indefinite"})}),(0,o.jsx)("animateTransform",{attributeName:"transform",attributeType:"XML",type:"translate",values:"0 0; 0 -19",dur:"2s",repeatCount:"indefinite"})]})},i)}function b(e){let{className:t}=e,[a,p]=(0,n.useState)(!1),[x,b]=(0,n.useState)("home"),[h,f]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=()=>{p(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,n.useEffect)(()=>{let e=()=>{let e=c.TD.map(e=>e.id),t=window.scrollY+100;for(let a of e){let e=document.getElementById(a);if(e){let{offsetTop:o,offsetHeight:n}=e;if(t>=o&&t<o+n){b(a);break}}}};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let g=e=>{let t=document.getElementById(e);if(t){let a="home"===e?0:t.offsetTop-80;window.scrollTo({top:a,behavior:"smooth"})}f(!1)};return(0,o.jsxs)(r.P.header,{className:(0,d.cn)("fixed top-0 left-0 w-full z-40 transition-all duration-300",a?"bg-background/50 backdrop-blur-md shadow-sm border-b border-border/50":"bg-transparent",t),initial:{y:-100},animate:{y:0},transition:{duration:.5},children:[(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,o.jsxs)(r.P.div,{className:"flex items-center space-x-3",whileHover:{scale:1.05},transition:{duration:.2},children:[(0,o.jsx)(m,{className:"w-8 h-8 lg:w-10 lg:h-10"}),(0,o.jsx)("span",{className:"text-lg lg:text-xl font-semibold text-foreground",children:"Narendra Chowdary"})]}),(0,o.jsx)("nav",{className:"hidden md:flex items-center space-x-1",children:c.TD.map(e=>(0,o.jsx)(r.P.button,{onClick:()=>g(e.id),className:(0,d.cn)("px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200","hover:bg-muted/50 hover:text-foreground",x===e.id?"text-foreground bg-muted/30":"text-muted-foreground"),whileHover:{scale:1.05},whileTap:{scale:.95},children:e.label},e.id))}),(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)(u,{}),(0,o.jsx)(r.P.button,{onClick:()=>{f(!h)},className:"md:hidden p-2 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Toggle mobile menu",children:(0,o.jsx)(i.N,{mode:"wait",children:h?(0,o.jsx)(r.P.div,{initial:{rotate:-90,opacity:0},animate:{rotate:0,opacity:1},exit:{rotate:90,opacity:0},transition:{duration:.2},children:(0,o.jsx)(s.A,{className:"w-5 h-5"})},"close"):(0,o.jsx)(r.P.div,{initial:{rotate:90,opacity:0},animate:{rotate:0,opacity:1},exit:{rotate:-90,opacity:0},transition:{duration:.2},children:(0,o.jsx)(l,{className:"w-5 h-5"})},"menu")})})]})]})}),(0,o.jsx)(i.N,{children:h&&(0,o.jsx)(r.P.div,{className:"md:hidden absolute top-full left-0 w-full bg-background/95 backdrop-blur-md border-b border-border/50",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,o.jsx)("nav",{className:"px-4 py-4 space-y-2",children:c.TD.map((e,t)=>(0,o.jsx)(r.P.button,{onClick:()=>g(e.id),className:(0,d.cn)("w-full text-left px-4 py-3 rounded-lg text-base font-medium transition-all duration-200","hover:bg-muted/50 hover:text-foreground",x===e.id?"text-foreground bg-muted/30":"text-muted-foreground"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},whileHover:{scale:1.02,x:4},whileTap:{scale:.98},children:e.label},e.id))})})})]})}function h(e){let{className:t}=e,[a,l]=(0,n.useState)(!1),[p,x]=(0,n.useState)("home");(0,n.useEffect)(()=>{let e=()=>{let e=c.TD.map(e=>e.id),t=window.scrollY+100;for(let a of e){let e=document.getElementById(a);if(e){let{offsetTop:o,offsetHeight:n}=e;if(t>=o&&t<o+n){x(a);break}}}};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,n.useEffect)(()=>{let e=()=>{l(e=>!e)},t=document.querySelector(".mobile-menu-btn");if(t)return t.addEventListener("click",e),()=>t.removeEventListener("click",e)},[]),(0,n.useEffect)(()=>(a?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[a]);let u=e=>{let t=document.getElementById(e);if(t){let e=t.offsetTop-80;window.scrollTo({top:e,behavior:"smooth"})}l(!1)},m={closed:{x:50,opacity:0},open:e=>({x:0,opacity:1,transition:{delay:.1*e,duration:.3}})};return(0,o.jsx)(i.N,{children:a&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.P.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden",variants:{closed:{opacity:0,transition:{duration:.3}},open:{opacity:1,transition:{duration:.3}}},initial:"closed",animate:"open",exit:"closed",onClick:()=>l(!1)}),(0,o.jsxs)(r.P.div,{className:(0,d.cn)("fixed top-0 right-0 h-full w-80 max-w-[80vw] bg-background/95 backdrop-blur-md","border-l border-border/50 shadow-2xl z-50 md:hidden",t),variants:{closed:{x:"100%",transition:{type:"spring",stiffness:400,damping:40}},open:{x:0,transition:{type:"spring",stiffness:400,damping:40}}},initial:"closed",animate:"open",exit:"closed",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border/50",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-foreground",children:"Menu"}),(0,o.jsx)(r.P.button,{onClick:()=>l(!1),className:"p-2 rounded-lg hover:bg-muted/50 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,o.jsx)(s.A,{className:"w-5 h-5"})})]}),(0,o.jsx)("nav",{className:"p-6",children:(0,o.jsx)("ul",{className:"space-y-2",children:c.TD.map((e,t)=>(0,o.jsx)(r.P.li,{custom:t,variants:m,initial:"closed",animate:"open",children:(0,o.jsx)(r.P.button,{onClick:()=>u(e.id),className:(0,d.cn)("w-full text-left px-4 py-3 rounded-lg text-base font-medium","transition-all duration-200 hover:bg-muted/50",p===e.id?"text-foreground bg-muted/30 border-l-4 border-primary":"text-muted-foreground hover:text-foreground"),whileHover:{scale:1.02,x:4,transition:{duration:.2}},whileTap:{scale:.98},children:(0,o.jsx)("span",{className:"capitalize",children:e.label})})},e.id))})}),(0,o.jsx)("div",{className:"absolute bottom-6 left-6 right-6",children:(0,o.jsx)("div",{className:"text-center text-sm text-muted-foreground",children:(0,o.jsx)("p",{children:"\xa9 2024 Narendra Chowdary"})})})]})]})})}var f=a(3587);function g(e){let{className:t}=e,a=(0,n.useRef)(null),i=(0,n.useRef)(null),s=(0,n.useRef)([]);return(0,n.useEffect)(()=>{let e=a.current;if(!e)return;let t=e.getContext("2d");if(!t)return;let o=()=>{e.width=window.innerWidth,e.height=window.innerHeight};o(),window.addEventListener("resize",o),s.current=[];for(let t=0;t<c._j.count;t++)s.current.push({x:Math.random()*e.width,y:Math.random()*e.height,vx:(Math.random()-.5)*c._j.speed,vy:(Math.random()-.5)*c._j.speed,size:Math.random()*(c._j.maxSize-c._j.minSize)+c._j.minSize,opacity:Math.random()*(c._j.opacity.max-c._j.opacity.min)+c._j.opacity.min,color:c._j.colors[Math.floor(Math.random()*c._j.colors.length)]});let n=()=>{t.clearRect(0,0,e.width,e.height),s.current.forEach(a=>{a.x+=a.vx,a.y+=a.vy,a.x<0&&(a.x=e.width),a.x>e.width&&(a.x=0),a.y<0&&(a.y=e.height),a.y>e.height&&(a.y=0),t.save(),t.globalAlpha=a.opacity,t.fillStyle=a.color,t.beginPath(),t.arc(a.x,a.y,a.size,0,2*Math.PI),t.fill(),t.restore()}),i.current=requestAnimationFrame(n)};return n(),()=>{window.removeEventListener("resize",o),i.current&&cancelAnimationFrame(i.current)}},[]),(0,o.jsx)(r.P.canvas,{ref:a,className:"fixed inset-0 pointer-events-none z-0 ".concat(t),initial:{opacity:0},animate:{opacity:1},transition:{duration:1},style:{background:"transparent"}})}function _(){let e=(0,n.useRef)(null),t=(0,n.useContext)(p),a=(null==t?void 0:t.theme)||"light";return(0,n.useEffect)(()=>{let t=e.current;if(!t)return;if(window.matchMedia("(hover: none)").matches||window.innerWidth<=768||navigator.maxTouchPoints>0){t.style.display="none";return}let a=e=>{t&&(t.style.transform="translate(".concat(e.clientX,"px, ").concat(e.clientY,"px)"))},o=()=>{t&&(t.style.opacity="0.8")},n=()=>{t&&(t.style.opacity="0")};return document.addEventListener("mousemove",a),document.addEventListener("mouseenter",o),document.addEventListener("mouseleave",n),()=>{document.removeEventListener("mousemove",a),document.removeEventListener("mouseenter",o),document.removeEventListener("mouseleave",n)}},[]),(0,o.jsx)("div",{ref:e,className:"\n        fixed top-0 left-0 w-3 h-3 rounded-full pointer-events-none z-[9999]\n        transform -translate-x-1/2 -translate-y-1/2 transition-transform duration-300 ease-out\n        opacity-80\n        ".concat("dark"===a?"bg-white shadow-[0_0_5px_rgba(255,255,255,0.5)]":"bg-black shadow-[0_0_5px_rgba(0,0,0,0.5)]","\n      "),style:{display:"block"}})}function y(e){let{children:t}=e,a=(0,n.useContext)(p),r=(0,f.H)(),i=(null==a?void 0:a.theme)||"light",{showHeader:s}=r;return(0,o.jsxs)("div",{className:"min-h-screen bg-background text-foreground transition-colors duration-300",children:[(0,o.jsx)(_,{}),"dark"===i&&(0,o.jsx)(g,{}),s&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(b,{}),(0,o.jsx)(h,{})]}),(0,o.jsx)("main",{className:"relative z-10",children:t})]})}function w(e){let{children:t}=e;return(0,o.jsx)(x,{children:(0,o.jsx)(f.R,{children:(0,o.jsx)(y,{children:t})})})}},1950:(e,t,a)=>{"use strict";a.d(t,{AP:()=>p,B2:()=>i,BH:()=>r,TD:()=>o,XW:()=>c,Yj:()=>n,_j:()=>s,cG:()=>l,w7:()=>d});let o=[{id:"home",label:"Home",href:"#home"},{id:"about",label:"About",href:"#about"},{id:"projects",label:"Projects",href:"#projects"},{id:"experience",label:"Experience",href:"#experience"},{id:"contact",label:"Contact",href:"#contact"}],n=[{code:"en",name:"English",greeting:"Hello"},{code:"te",name:"Telugu",greeting:"నమస్కారం"},{code:"hi",name:"Hindi",greeting:"नमस्ते"},{code:"ta",name:"Tamil",greeting:"வணக்கம்"},{code:"kn",name:"Kannada",greeting:"ನಮಸ್ಕಾರ"},{code:"ml",name:"Malayalam",greeting:"നമസ്കാരം"},{code:"gu",name:"Gujarati",greeting:"નમસ્તે"},{code:"or",name:"Odia",greeting:"ନମସ୍କାର"},{code:"ur",name:"Urdu",greeting:"السلام علیکم"},{code:"ja",name:"Japanese",greeting:"こんにちは"},{code:"ko",name:"Korean",greeting:"안녕하세요"},{code:"ru",name:"Russian",greeting:"Здравствуйте"},{code:"ta-lk",name:"Sri Lankan Tamil",greeting:"Vanakkam"}],r={fast:150,normal:250,slow:400,languageSwitch:250,pageTransition:500,scrollIndicator:1e3,particleAnimation:6e4},i={defaultTheme:"light",storageKey:"portfolio-theme",transitionDuration:"0.3s"},s={count:50,maxSize:3,minSize:1,speed:.5,opacity:{min:.1,max:.8},colors:["#ffffff","#f0f0f0","#e0e0e0"]},l={validation:{email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^[\+]?[1-9][\d]{0,15}$/,minNameLength:2,maxNameLength:50,minMessageLength:10,maxMessageLength:1e3},placeholders:{name:"Your Name",email:"<EMAIL>",subject:"Subject",message:"Your message..."}},d=[{platform:"GitHub",url:"https://github.com/nrenx",icon:"Github",label:"GitHub Profile"},{platform:"LinkedIn",url:"https://linkedin.com/in/bollineninarendrachowdary",icon:"Linkedin",label:"LinkedIn Profile"},{platform:"Twitter",url:"https://x.com/___CHOWDARY___",icon:"Twitter",label:"Twitter Profile"},{platform:"Email",url:"mailto:<EMAIL>",icon:"Mail",label:"Send Email"}],c={smoothScrollDuration:800,scrollOffset:80,scrollThreshold:100,indicatorFadeDistance:200},p={resume:"/assets/resume/resume.pdf",images:{profile:"/assets/images/profile.jpg",profileHover:"/assets/images/profile-hover.jpg",ogImage:"/assets/images/og-image.jpg",favicon:"/favicon.ico"},icons:{logo:"/icons/logo.svg",logoLight:"/icons/logo-light.svg",logoDark:"/icons/logo-dark.svg"},wallpapers:{macosDesktop:"/assets/macOS-wallpaper/wallpaperflare.com_wallpaper (1).jpg"}}},3136:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,5266,23)),Promise.resolve().then(a.t.bind(a,8593,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,1654))},3587:(e,t,a)=>{"use strict";a.d(t,{H:()=>s,R:()=>i});var o=a(5155),n=a(2115);let r=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,n.useState)(!0),[s,l]=(0,n.useState)(!1);return(0,o.jsx)(r.Provider,{value:{showLanding:a,showHeader:s,setShowLanding:i,setShowHeader:l,handleLandingComplete:()=>{i(!1),setTimeout(()=>{l(!0)},200)}},children:t})}function s(){let e=(0,n.useContext)(r);if(void 0===e)throw Error("useLanding must be used within a LandingProvider");return e}},5266:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_dcbc8f",variable:"__variable_dcbc8f"}},8593:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_0a793b",variable:"__variable_0a793b"}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var o=a(2596),n=a(9688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,o.$)(t))}a(1890)}},e=>{var t=t=>e(e.s=t);e.O(0,[310,43,26,441,684,358],()=>t(3136)),_N_E=e.O()}]);