(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1950:(e,t,a)=>{"use strict";a.d(t,{AP:()=>p,B2:()=>o,BH:()=>s,TD:()=>r,XW:()=>m,Yj:()=>n,_j:()=>l,cG:()=>d,w7:()=>c});var i=a(9434);let r=[{id:"home",label:"Home",href:"#home"},{id:"about",label:"About",href:"#about"},{id:"projects",label:"Projects",href:"#projects"},{id:"experience",label:"Experience",href:"#experience"},{id:"contact",label:"Contact",href:"#contact"}],n=[{code:"en",name:"English",greeting:"Hello"},{code:"te",name:"Telugu",greeting:"నమస్కారం"},{code:"hi",name:"Hindi",greeting:"नमस्ते"},{code:"ta",name:"Tamil",greeting:"வணக்கம்"},{code:"kn",name:"Kannada",greeting:"ನಮಸ್ಕಾರ"},{code:"ml",name:"Malayalam",greeting:"നമസ്കാരം"},{code:"gu",name:"Gujarati",greeting:"નમસ્તે"},{code:"or",name:"Odia",greeting:"ନମସ୍କାର"},{code:"ur",name:"Urdu",greeting:"السلام علیکم"},{code:"ja",name:"Japanese",greeting:"こんにちは"},{code:"ko",name:"Korean",greeting:"안녕하세요"},{code:"ru",name:"Russian",greeting:"Здравствуйте"},{code:"ta-lk",name:"Sri Lankan Tamil",greeting:"Vanakkam"}],s={fast:150,normal:250,slow:400,languageSwitch:250,pageTransition:500,scrollIndicator:1e3,particleAnimation:6e4},o={defaultTheme:"light",storageKey:"portfolio-theme",transitionDuration:"0.3s"},l={count:50,maxSize:3,minSize:1,speed:.5,opacity:{min:.1,max:.8},colors:["#ffffff","#f0f0f0","#e0e0e0"]},d={validation:{email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^[\+]?[1-9][\d]{0,15}$/,minNameLength:2,maxNameLength:50,minMessageLength:10,maxMessageLength:1e3},placeholders:{name:"Your Name",email:"<EMAIL>",subject:"Subject",message:"Your message..."}},c=[{platform:"GitHub",url:"https://github.com/nrenx",icon:"Github",label:"GitHub Profile"},{platform:"LinkedIn",url:"https://linkedin.com/in/bollineninarendrachowdary",icon:"Linkedin",label:"LinkedIn Profile"},{platform:"Twitter",url:"https://x.com/___CHOWDARY___",icon:"Twitter",label:"Twitter Profile"},{platform:"Email",url:"mailto:<EMAIL>",icon:"Mail",label:"Send Email"}],m={smoothScrollDuration:800,scrollOffset:80,scrollThreshold:100,indicatorFadeDistance:200},p={resume:(0,i.O)("/assets/resume/resume.pdf"),images:{profile:(0,i.O)("/assets/images/Finding joy in the simplicity of the sea ............beach bridge ocean smile sunny monument collage sunset sunrise travelphotography travel.jpg"),profileHover:(0,i.O)("/assets/images/Finding paradise wherever the waves take me. . . . . . . . . . . . . . . .beachbound beachlife beach beachdreaming ocean paradise wavesfordays explore rainyday shorelineadventures seasideescape beach.jpg"),ogImage:(0,i.O)("/assets/images/Finding joy in the simplicity of the sea ............beach bridge ocean smile sunny monument collage sunset sunrise travelphotography travel.jpg"),favicon:(0,i.O)("/favicon.ico")},icons:{logo:(0,i.O)("/icons/logo.svg"),logoLight:(0,i.O)("/icons/logo-light.svg"),logoDark:(0,i.O)("/icons/logo-dark.svg")},wallpapers:{macosDesktop:(0,i.O)("/assets/macOS-wallpaper/wallpaperflare.com_wallpaper (1).jpg"),macosDesktopAlt:(0,i.O)("/assets/macOS-wallpaper/wallpaperflare.com_wallpaper.jpg")}}},3587:(e,t,a)=>{"use strict";a.d(t,{H:()=>o,R:()=>s});var i=a(5155),r=a(2115);let n=(0,r.createContext)(void 0);function s(e){let{children:t}=e,[a,s]=(0,r.useState)(!0),[o,l]=(0,r.useState)(!1);return(0,i.jsx)(n.Provider,{value:{showLanding:a,showHeader:o,setShowLanding:s,setShowHeader:l,handleLandingComplete:()=>{s(!1),setTimeout(()=>{l(!0)},200)}},children:t})}function o(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useLanding must be used within a LandingProvider");return e}},6310:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>K});var i=a(5155),r=a(2115),n=a(760),s=a(6001),o=a(1950);function l(e){let{onComplete:t}=e,[a,l]=(0,r.useState)(0),[d,c]=(0,r.useState)(!0),[m,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=setInterval(()=>{l(e=>(e+1)%o.Yj.length)},o.BH.languageSwitch),a=setTimeout(()=>{p(!0),setTimeout(()=>{c(!1),setTimeout(()=>{null==t||t()},800)},300)},1500);return()=>{clearInterval(e),clearTimeout(a)}},[t]);let h={initial:{scale:0,opacity:0},animate:{scale:1,opacity:.7,x:200*Math.random()-100,y:200*Math.random()-100,transition:{duration:1.5,ease:"easeOut"}},exit:{scale:0,opacity:0,transition:{duration:.5}}};return(0,i.jsx)(n.N,{children:d&&(0,i.jsxs)(s.P.div,{className:"fixed inset-0 z-[2000] flex items-center justify-center overflow-hidden",style:{background:"linear-gradient(135deg, #000, #222)",perspective:"1000px"},variants:{visible:{y:0,rotateX:0,opacity:1,transition:{duration:.5,ease:"easeOut"}},hidden:{y:"-100%",rotateX:10,opacity:0,transition:{duration:1.2,ease:[.6,.1,.3,1]}}},initial:"visible",animate:"visible",exit:"hidden",children:[(0,i.jsx)(s.P.div,{className:"absolute w-[200%] h-[200%] opacity-20",style:{background:"radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(0,0,0,0) 70%)"},variants:{animate:{x:["-25%","-75%"],y:["-25%","-75%"],transition:{duration:5,repeat:1/0,ease:"linear"}}},animate:"animate"}),(0,i.jsx)("div",{className:"relative h-[120px] w-full text-center",children:(0,i.jsx)(n.N,{mode:"wait",children:(0,i.jsx)(s.P.div,{className:"absolute inset-0 flex items-center justify-center",variants:{hidden:{opacity:0,scale:1,transition:{duration:.3,ease:"easeInOut"}},visible:{opacity:1,scale:1.1,transition:{duration:.3,ease:"easeInOut"}}},initial:"hidden",animate:"visible",exit:"hidden",children:(0,i.jsx)("h1",{className:"text-5xl md:text-6xl font-semibold text-white/95",style:{textShadow:"0 4px 12px rgba(0, 0, 0, 0.3)"},children:o.Yj[a].greeting})},a)})}),(0,i.jsx)(n.N,{children:m&&(0,i.jsx)(i.Fragment,{children:Array.from({length:20}).map((e,t)=>(0,i.jsx)(s.P.div,{className:"absolute w-1.5 h-1.5 bg-white/70 rounded-full pointer-events-none",style:{left:"".concat(50+(Math.random()-.5)*20,"%"),top:"".concat(50+(Math.random()-.5)*20,"%")},variants:h,initial:"initial",animate:"animate",exit:"exit"},t))})})]})})}var d=a(9946);let c=(0,d.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),m=(0,d.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]);var p=a(9434);let h=(0,d.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);function u(e){let{className:t}=e,[a,l]=(0,r.useState)(!0),[d,c]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=()=>{let e=window.scrollY,t=document.getElementById("home");if(t){let a=e<t.offsetHeight-100;c(a),l(a&&e<o.XW.indicatorFadeDistance)}};return e(),window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let m={animate:{y:[0,8,0],transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}}};return(0,i.jsx)(n.N,{children:a&&d&&(0,i.jsxs)(s.P.div,{className:(0,p.cn)("absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center cursor-pointer z-10",t),variants:{visible:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}},hidden:{opacity:0,y:20,transition:{duration:.3,ease:"easeIn"}}},initial:"hidden",animate:"visible",exit:"hidden",onClick:()=>{let e=document.getElementById("about");e&&e.scrollIntoView({behavior:"smooth",block:"start"})},whileHover:{scale:1.1},whileTap:{scale:.95},children:[(0,i.jsx)(s.P.span,{className:"text-sm text-muted-foreground mb-2 font-medium",variants:{animate:{opacity:[.7,1,.7],transition:{duration:2,repeat:1/0,ease:"easeInOut"}}},animate:"animate",children:"Scroll Down"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(s.P.div,{variants:m,animate:"animate",children:(0,i.jsx)(h,{className:"w-6 h-6 text-muted-foreground"})}),(0,i.jsx)(s.P.div,{className:"absolute top-0 left-0",variants:m,animate:"animate",style:{marginTop:"-8px"},transition:{delay:.2},children:(0,i.jsx)(h,{className:"w-6 h-6 text-muted-foreground opacity-60"})})]}),(0,i.jsx)(s.P.div,{className:"absolute inset-0 rounded-full bg-primary/20 blur-xl opacity-0",whileHover:{opacity:1},transition:{duration:.3}})]})})}let g=(0,d.A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),x=(0,d.A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),b=(0,d.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),f=(0,d.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);function y(e){let{className:t}=e,a={Github:g,Linkedin:x,Twitter:b,Mail:f},r={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,i.jsx)(s.P.div,{className:(0,p.cn)("flex items-center gap-4",t),variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",children:o.w7.map((e,t)=>{let n=a[e.icon];return(0,i.jsxs)(s.P.a,{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"group relative p-3 rounded-full bg-muted/50 hover:bg-muted transition-all duration-300",variants:r,whileHover:{scale:1.1,rotate:5},whileTap:{scale:.95},"aria-label":e.label,children:[(0,i.jsx)(s.P.div,{className:"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300",initial:!1,animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,i.jsx)(n,{className:"w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors duration-300 relative z-10"}),(0,i.jsxs)(s.P.div,{className:"absolute -top-12 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap",initial:{y:10,opacity:0},whileHover:{y:0,opacity:1},children:[e.platform,(0,i.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover"})]})]},e.platform)})})}function v(e){let{className:t}=e;return(0,i.jsxs)("section",{id:"home",className:(0,p.cn)("min-h-screen flex items-center justify-center relative overflow-hidden pt-16 lg:pt-20",t),children:[(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full",children:(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row items-center justify-between min-h-[calc(100vh-4rem)] lg:min-h-[calc(100vh-5rem)] gap-8",children:[(0,i.jsx)(s.P.div,{className:"image-container flex-shrink-0 flex justify-center lg:justify-center order-1 lg:order-2",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:1,delay:.4},children:(0,i.jsxs)(s.P.div,{className:"morph-container group relative w-[300px] h-[300px] overflow-hidden bg-cover bg-no-repeat bg-center border-4 border-[#2d2e32]",style:{borderRadius:"60% 40% 30% 70%/60% 30% 70% 40%",backgroundImage:"url('data:image/svg+xml,".concat(encodeURIComponent('\n                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 300">\n                      <defs>\n                        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">\n                          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />\n                          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />\n                        </linearGradient>\n                      </defs>\n                      <rect width="300" height="300" fill="url(#grad1)" />\n                      <circle cx="150" cy="120" r="40" fill="rgba(255,255,255,0.1)" />\n                      <ellipse cx="150" cy="200" rx="60" ry="80" fill="rgba(255,255,255,0.05)" />\n                      <text x="150" y="160" text-anchor="middle" fill="white" font-size="48">\uD83D\uDC68‍\uD83D\uDCBB</text>\n                    </svg>\n                  '),"')")},animate:{borderRadius:["60% 40% 30% 70%/60% 30% 70% 40%","40% 60% 70% 30%/40% 70% 30% 60%","30% 60% 70% 40%/50% 60% 30% 40%","55% 45% 40% 60%/45% 30% 60% 55%","60% 40% 30% 70%/60% 30% 70% 40%"]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},whileHover:{scale:1.05},children:[(0,i.jsx)("div",{className:"background-image absolute top-0 left-0 w-full h-full object-cover z-[1] transition-opacity duration-500 ease-in-out group-hover:opacity-0",style:{backgroundImage:"url('".concat(o.AP.images.profile,"')"),backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}}),(0,i.jsx)("div",{className:"second-image absolute top-0 left-0 w-full h-full object-cover z-[2] opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100",style:{backgroundImage:"url('".concat(o.AP.images.profileHover,"')"),backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}})]})}),(0,i.jsxs)(s.P.div,{className:"text-container space-y-6 flex-1 max-w-2xl order-2 lg:order-1",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:[(0,i.jsxs)(s.P.div,{className:"inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm font-medium",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.4},children:[(0,i.jsx)(s.P.span,{className:"w-2 h-2 bg-green-500 rounded-full",animate:{scale:[1,1.2,1],opacity:[1,.7,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),"Available for work"]}),(0,i.jsxs)(s.P.h1,{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},children:["Hi, I'm Narendra"," ",(0,i.jsx)(s.P.span,{className:"inline-block",animate:{rotate:[0,14,-8,14,-4,10,0]},transition:{duration:2.5,repeat:1/0,repeatDelay:3,ease:"easeInOut"},children:"\uD83D\uDC4B"})]}),(0,i.jsxs)(s.P.div,{className:"bio-container space-y-3",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:[(0,i.jsx)("h2",{className:"text-xl sm:text-2xl font-semibold text-foreground",children:"No-Code Developer | AI Prompt Engineer | Mobile App Creator"}),(0,i.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed max-w-2xl",children:"Recent Computer Science and Engineering graduate specializing in AI-assisted development and no-code solutions. I leverage modern AI tools to build efficient SaaS applications, mobile apps, and automation workflows. With expertise in cloud backends and API integration, I create cost-effective digital solutions with minimal traditional coding."})]}),(0,i.jsxs)(s.P.div,{className:"button-container flex flex-col sm:flex-row gap-4 mt-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},children:[(0,i.jsxs)(s.P.button,{onClick:()=>{let e=document.getElementById("contact");e&&e.scrollIntoView({behavior:"smooth"})},className:"button get-in-touch group relative px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,i.jsxs)("span",{className:"relative z-10 flex items-center gap-2",children:["Get in Touch",(0,i.jsx)(c,{className:"w-4 h-4 transition-transform group-hover:translate-x-1"})]}),(0,i.jsx)(s.P.div,{className:"absolute inset-0 bg-gradient-to-r from-primary to-primary/80",initial:{x:"-100%"},whileHover:{x:0},transition:{duration:.3}})]}),(0,i.jsxs)(s.P.button,{onClick:()=>{let e=document.getElementById("projects");e&&e.scrollIntoView({behavior:"smooth"})},className:"button view-projects group relative px-8 py-3 border border-border text-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-primary/50",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,i.jsxs)("span",{className:"relative z-10 flex items-center gap-2",children:["View Projects",(0,i.jsx)(m,{className:"w-4 h-4 transition-transform group-hover:rotate-12"})]}),(0,i.jsx)(s.P.div,{className:"absolute inset-0 bg-muted/50",initial:{x:"-100%"},whileHover:{x:0},transition:{duration:.3}})]})]}),(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1.2},children:(0,i.jsx)(y,{})})]})]})}),(0,i.jsx)(u,{})]})}let w=(0,d.A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),j=(0,d.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),N=(0,d.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);function k(e){let{className:t}=e,a={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,i.jsxs)("section",{id:"about",className:(0,p.cn)("min-h-screen py-20 bg-muted/20 relative overflow-hidden",t),children:[(0,i.jsx)("div",{className:"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/5 to-transparent"}),(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,i.jsxs)(s.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:[(0,i.jsxs)(s.P.div,{variants:a,className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-foreground mb-4",children:"About Me"}),(0,i.jsx)("div",{className:"w-20 h-1 bg-primary mx-auto rounded-full"})]}),(0,i.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 lg:gap-16",children:[(0,i.jsxs)(s.P.div,{variants:a,className:"space-y-8",children:[(0,i.jsx)("div",{className:"prose prose-lg dark:prose-invert max-w-none",children:(0,i.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed",children:"Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, and automation workflows. I'm seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, and mobile app creation to deliver cost-effective and innovative solutions."})}),(0,i.jsxs)("div",{className:"education-container",children:[(0,i.jsxs)(s.P.h3,{className:"text-2xl font-semibold text-foreground mb-6 flex items-center gap-2",variants:a,children:[(0,i.jsx)(w,{className:"w-6 h-6 text-primary"}),"Education"]}),(0,i.jsx)("div",{className:"space-y-6",children:[{year:"Expected 2026",degree:"Bachelor of Engineering in Computer Science and Engineering",institution:"NBKRIST College Autonomous",grade:"CGPA: 8.2 (Current)"},{year:"Graduated 2022",degree:"Intermediate",institution:"Narayana Junior College, State Board",grade:"CGPA: 5.55"},{year:"Graduated 2020",degree:"SSC",institution:"Narayana EM High School, State Board",grade:"CGPA: 9.88"}].map((e,t)=>(0,i.jsxs)(s.P.div,{className:"education-item flex gap-4 p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors",variants:a,whileHover:{scale:1.02},transition:{duration:.2},children:[(0,i.jsx)("div",{className:"education-year flex-shrink-0",children:(0,i.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-primary",children:[(0,i.jsx)(j,{className:"w-4 h-4"}),e.year]})}),(0,i.jsxs)("div",{className:"education-details flex-1",children:[(0,i.jsx)("h4",{className:"font-semibold text-foreground mb-1",children:e.degree}),(0,i.jsx)("p",{className:"text-muted-foreground mb-1",children:e.institution}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:e.grade})]})]},t))})]})]}),(0,i.jsxs)(s.P.div,{variants:a,className:"skills-container",children:[(0,i.jsxs)(s.P.h3,{className:"text-2xl font-semibold text-foreground mb-6 flex items-center gap-2",variants:a,children:[(0,i.jsx)(N,{className:"w-6 h-6 text-primary"}),"Skills"]}),(0,i.jsx)("div",{className:"skills-grid space-y-6",children:[{title:"No-Code/Low-Code",skills:["SaaS development using AI-assisted tools & platforms"]},{title:"Cloud & Backend",skills:["Supabase & Firebase – auth, DB, storage","API Integration & key management","Cost-optimized usage of 3rd-party services"]},{title:"Mobile Development",skills:["Android & iOS dev via AI tools","Android Studio","Xcode"]},{title:"AI & Automation",skills:["AI Prompt Engineering with low-iteration design","Workflow automation using n8n","Telegram bots for info delivery & engagement"]},{title:"Web Development",skills:["HTML","CSS","JavaScript","Basic front-end tasks"]}].map((e,t)=>(0,i.jsxs)(s.P.div,{className:"skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors",variants:a,whileHover:{scale:1.02},transition:{duration:.2},children:[(0,i.jsx)("h4",{className:"font-semibold text-foreground mb-3",children:e.title}),(0,i.jsx)("ul",{className:"skills-list space-y-2",children:e.skills.map((e,t)=>(0,i.jsxs)("li",{className:"text-muted-foreground flex items-start gap-2",children:[(0,i.jsx)("span",{className:"w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"}),e]},t))})]},t))})]})]})]})})]})}let P=(0,d.A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);var M=a(4416);let S=(0,d.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),I=(0,d.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);function A(e){let{id:t,title:a,children:n,position:o,size:l,isMinimized:d,isMaximized:c,zIndex:m,onClose:h,onMinimize:u,onMaximize:g,onPositionChange:x,onFocus:b,className:f}=e,y=(0,r.useRef)(null),v=(0,r.useRef)(!1);if((0,r.useEffect)(()=>{let e=()=>{if(y.current){let e=window.innerWidth-l.width,t=window.innerHeight-l.height;(o.x>e||o.y>t)&&x({x:Math.max(0,Math.min(o.x,e)),y:Math.max(0,Math.min(o.y,t))})}};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[o,l,x]),d)return null;let w={initial:{scale:.8,opacity:0,x:o.x,y:o.y},animate:{scale:1,opacity:1,x:o.x,y:o.y,transition:{type:"spring",stiffness:300,damping:30}},exit:{scale:.8,opacity:0,transition:{duration:.2}}};return(0,i.jsxs)(s.P.div,{ref:y,className:(0,p.cn)("absolute bg-background/95 backdrop-blur-md rounded-lg shadow-2xl border border-border/50 overflow-hidden","select-none",f),style:{width:c?"100vw":l.width,height:c?"calc(100vh - 100px)":l.height,zIndex:m},variants:w,initial:"initial",animate:"animate",exit:"exit",drag:!c,dragMomentum:!1,dragElastic:0,dragConstraints:{left:0,right:window.innerWidth-l.width,top:0,bottom:window.innerHeight-l.height},onDragStart:()=>{v.current=!0,b()},onDragEnd:(e,t)=>{v.current=!1,x({x:Math.max(0,Math.min(o.x+t.offset.x,window.innerWidth-l.width)),y:Math.max(0,Math.min(o.y+t.offset.y,window.innerHeight-l.height))})},onClick:()=>{v.current||b()},whileHover:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25)"},children:[(0,i.jsxs)("div",{className:"flex items-center justify-between h-12 px-4 bg-muted/30 border-b border-border/50 cursor-move",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(s.P.button,{className:"w-3 h-3 bg-red-500 rounded-full hover:bg-red-600 transition-colors",onClick:e=>{e.stopPropagation(),h()},whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"Close window",children:(0,i.jsx)(M.A,{className:"w-2 h-2 text-red-900 opacity-0 hover:opacity-100 transition-opacity mx-auto"})}),(0,i.jsx)(s.P.button,{className:"w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-600 transition-colors",onClick:e=>{e.stopPropagation(),u()},whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"Minimize window",children:(0,i.jsx)(S,{className:"w-2 h-2 text-yellow-900 opacity-0 hover:opacity-100 transition-opacity mx-auto"})}),(0,i.jsx)(s.P.button,{className:"w-3 h-3 bg-green-500 rounded-full hover:bg-green-600 transition-colors",onClick:e=>{e.stopPropagation(),g()},whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"Maximize window",children:(0,i.jsx)(I,{className:"w-2 h-2 text-green-900 opacity-0 hover:opacity-100 transition-opacity mx-auto"})})]}),(0,i.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 pointer-events-none",children:(0,i.jsx)("h3",{className:"text-sm font-medium text-foreground truncate max-w-48",children:a})}),(0,i.jsx)("div",{className:"w-16"})]}),(0,i.jsx)("div",{className:"h-full overflow-hidden",style:{height:"calc(100% - 48px)"},children:n})]})}let C=(0,d.A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),z=(0,d.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),T=(0,d.A)("terminal",[["path",{d:"M12 19h8",key:"baeox8"}],["path",{d:"m4 17 6-6-6-6",key:"1yngyt"}]]);function D(e){let{openWindows:t,onWindowRestore:a,onWallpaperClick:o,className:l}=e,[d,c]=(0,r.useState)(null),m=[{id:"finder",name:"Finder",icon:P,color:"text-blue-500",action:()=>console.log("Finder clicked")},{id:"wallpaper",name:"Desktop Wallpaper",icon:C,color:"text-purple-600",action:()=>null==o?void 0:o()},{id:"calculator",name:"Calculator",icon:z,color:"text-gray-700",action:()=>window.open("https://calculator.net/","_blank")},{id:"terminal",name:"Terminal",icon:T,color:"text-black",action:()=>console.log("Terminal clicked")},{id:"github",name:"GitHub",icon:g,color:"text-gray-900",action:()=>window.open("https://github.com/nrenx","_blank")},{id:"linkedin",name:"LinkedIn",icon:x,color:"text-blue-600",action:()=>window.open("https://linkedin.com/in/bollineninarendrachowdary","_blank")},{id:"mail",name:"Mail",icon:f,color:"text-blue-500",action:()=>window.open("mailto:<EMAIL>","_blank")}],h=t.filter(e=>e.isMinimized),u=e=>{e.action()},b=e=>{a(e)};return(0,i.jsx)("div",{className:(0,p.cn)("absolute bottom-4 left-1/2 transform -translate-x-1/2",l),children:(0,i.jsx)(s.P.div,{className:"bg-white/10 backdrop-blur-md rounded-2xl px-3 py-2 border border-white/20 shadow-2xl",initial:{y:100,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5,duration:.5},children:(0,i.jsxs)("div",{className:"flex items-end gap-1",children:[m.map(e=>{let t=e.icon,a=d===e.id;return(0,i.jsxs)(s.P.div,{className:"relative flex flex-col items-center",onMouseEnter:()=>c(e.id),onMouseLeave:()=>c(null),children:[(0,i.jsx)(n.N,{children:a&&(0,i.jsxs)(s.P.div,{className:"absolute -top-12 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.2},children:[e.name,(0,i.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"})]})}),(0,i.jsx)(s.P.button,{className:(0,p.cn)("w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center","hover:bg-white/30 transition-colors cursor-pointer","backdrop-blur-sm border border-white/10"),onClick:()=>u(e),whileHover:{scale:1.2,y:-8},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:25},children:(0,i.jsx)(t,{className:(0,p.cn)("w-6 h-6",e.color)})})]},e.id)}),h.length>0&&(0,i.jsx)("div",{className:"w-px h-8 bg-white/30 mx-1 self-center"}),h.map(e=>(0,i.jsxs)(s.P.div,{className:"relative flex flex-col items-center",onMouseEnter:()=>c("minimized-".concat(e.id)),onMouseLeave:()=>c(null),children:[(0,i.jsx)(n.N,{children:d==="minimized-".concat(e.id)&&(0,i.jsxs)(s.P.div,{className:"absolute -top-12 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.2},children:[e.title,(0,i.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"})]})}),(0,i.jsxs)(s.P.button,{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10 relative",onClick:()=>b(e.id),whileHover:{scale:1.2,y:-8},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:25},initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},children:[(0,i.jsx)(P,{className:"w-6 h-6 text-blue-500"}),(0,i.jsx)("div",{className:"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full"})]})]},"minimized-".concat(e.id)))]})})})}let H=[{id:"saas-dashboard",title:"Trade Book Ledge",description:"SaaS business management platform for trade operations",longDescription:"A comprehensive SaaS business management platform designed for trade operations. Features inventory management, financial tracking, customer relationship management, and automated reporting capabilities built with modern no-code tools.",technologies:["Bubble.io","Supabase","Zapier","Chart.js","Stripe API"],features:["Inventory and stock management","Financial tracking and reporting","Customer relationship management","Automated invoicing and billing","Real-time business analytics","Multi-user access control"],githubUrl:"https://github.com/nrenx/Trade-Book-Ledge",liveUrl:"https://nrenx.github.io/Trade-Book-Ledge/",imageUrl:"/assets/images/projects/saas-dashboard.jpg",category:"web",status:"completed",startDate:"2023-08",endDate:"2023-11"},{id:"mobile-fitness-app",title:"NBKRIST Student Portal",description:"Academic portal for student management and services",longDescription:"A comprehensive academic portal designed for NBKRIST students to access academic services, track progress, and manage their educational journey. Built with modern mobile-first design and cloud backend integration.",technologies:["FlutterFlow","Firebase","Google Cloud","Stripe","Academic APIs"],features:["Student profile and academic records","Course registration and scheduling","Grade tracking and progress analytics","Fee payment and financial management","Campus news and announcements","Digital library access"],githubUrl:"https://github.com/nrenx/nbkrist-student-portal",liveUrl:"https://nbkrstudenthub.me/",imageUrl:"/assets/images/projects/fitness-app.jpg",category:"mobile",status:"completed",startDate:"2023-05",endDate:"2023-09"},{id:"automation-workflow",title:"NBKRIST Telegram Bot",description:"Automated Telegram bot for student attendance tracking",longDescription:"An intelligent Telegram bot designed for NBKRIST students to automate attendance tracking and provide quick access to academic information. Built with Python and integrated with the student portal system.",technologies:["Python","Telegram Bot API","Web Scraping","Automation","Student Portal Integration"],features:["Automated attendance tracking","Real-time academic updates","Student information retrieval","Telegram bot interface","Integration with NBKRIST portal","Automated notifications"],githubUrl:"https://github.com/nrenx/nbkrist-telegram-bot",liveUrl:void 0,imageUrl:"/assets/images/projects/automation.jpg",category:"other",status:"completed",startDate:"2023-12",endDate:"2024-02"},{id:"ai-automation-internship",title:"AI Weather Reporter",description:"Intelligent weather automation system with AI-powered insights",longDescription:"An intelligent weather automation system that collects user information, fetches real-time weather data, generates AI-powered insights, and delivers personalized weather reports via email. Built as a qualifying task for an AI Automation Internship.",technologies:["React","TypeScript","n8n","Supabase","OpenRouter AI","WeatherAPI"],features:["Real-time weather and air quality data","AI-powered weather commentary using Gemini Flash 1.5","Automated email delivery with HTML templates","Smart duplicate prevention system","Complete audit trail in Supabase database","Professional responsive UI with shadcn/ui"],githubUrl:"https://github.com/nrenx/ai-automation-internship",liveUrl:void 0,imageUrl:"/assets/images/projects/chatbot.jpg",category:"web",status:"completed",startDate:"2024-01",endDate:"2024-03"},{id:"designer-shortlist-platform",title:"Designer Shortlist Platform",description:"Platform for shortlisting and managing design talent",longDescription:"A modern platform that helps companies shortlist and manage design talent efficiently. Features portfolio reviews, skill assessments, and streamlined hiring workflows for design teams.",technologies:["TypeScript","React","Modern UI/UX","Portfolio Management","Assessment Tools"],features:["Designer portfolio showcase","Skill-based filtering and search","Assessment and review system","Hiring workflow management","Team collaboration tools","Analytics and reporting"],githubUrl:"https://github.com/nrenx/designer-shortlist-platform",liveUrl:void 0,imageUrl:"/assets/images/projects/ecommerce.jpg",category:"web",status:"completed",startDate:"2024-04",endDate:"2024-05"},{id:"portfolio-website",title:"Interactive Portfolio Website",description:"Modern portfolio with macOS-style interface",longDescription:"A modern, interactive portfolio website featuring a macOS-style interface, smooth animations, and responsive design. Built with React, Next.js, and Framer Motion.",technologies:["React","Next.js","TypeScript","Tailwind CSS","Framer Motion"],features:["macOS-style interface simulation","Multi-language landing animation","Dark/light theme switching","Smooth scroll animations","Responsive design","Contact form with validation"],githubUrl:"https://github.com/nrenx/portfilio.git",liveUrl:"https://nrenx.github.io/portfilio/",imageUrl:"/assets/images/projects/portfolio.jpg",category:"web",status:"completed",startDate:"2024-03",endDate:"2024-05"}];function E(e){let{className:t}=e,[a,l]=(0,r.useState)([]),[d,c]=(0,r.useState)(100),[m,h]=(0,r.useState)(o.AP.wallpapers.macosDesktop),[u,g]=(0,r.useState)([]),[x,b]=(0,r.useState)(!1),f=()=>{let e=window.innerWidth<768,t=e?120:130,a=e?50:100,i=H.find(e=>"saas-dashboard"===e.id),r=H.find(e=>"mobile-fitness-app"===e.id),n=H.find(e=>"portfolio-website"===e.id),s=H.find(e=>"ai-automation-internship"===e.id),o=["saas-dashboard","mobile-fitness-app","portfolio-website","ai-automation-internship"],l=H.filter(e=>!o.includes(e.id));return e?[{id:"trade-book-ledge",name:"Trade Book Ledge",project:i,position:{x:a,y:80},type:"project"},{id:"nbkrist-portal",name:"NBKRIST Student Portal",project:r,position:{x:a,y:200},type:"project"},{id:"ai-automation-internship",name:"AI Automation Internship",project:s,position:{x:a,y:320},type:"project"},{id:"portfolio-website",name:"Interactive Portfolio Website",project:n,position:{x:a,y:440},type:"project"},{id:"more-projects",name:"More Projects",projects:l,position:{x:a,y:560},type:"special"}]:[{id:"trade-book-ledge",name:"Trade Book Ledge",project:i,position:{x:a,y:80},type:"project"},{id:"nbkrist-portal",name:"NBKRIST Student Portal",project:r,position:{x:a+t,y:80},type:"project"},{id:"ai-automation-internship",name:"AI Weather Reporter",project:s,position:{x:a,y:220},type:"project"},{id:"portfolio-website",name:"Interactive Portfolio Website",project:n,position:{x:a+t,y:220},type:"project"},{id:"more-projects",name:"More Projects",projects:l,position:{x:a+t/2,y:360},type:"special"}]},[y,v]=(0,r.useState)(()=>{let e="2x2+1-grid";{let t=sessionStorage.getItem("macos-layout-version"),a=sessionStorage.getItem("macos-folder-positions");if(t===e&&a)try{return JSON.parse(a)}catch(e){console.warn("Failed to parse saved folder positions")}else sessionStorage.setItem("macos-layout-version",e)}return f()});(0,r.useEffect)(()=>{(async()=>{try{let e=[{id:"wallpaper-1",name:"Default Wallpaper",path:o.AP.wallpapers.macosDesktop},{id:"wallpaper-2",name:"Alternative Wallpaper",path:o.AP.wallpapers.macosDesktopAlt}];g(e)}catch(e){console.error("Failed to load wallpapers:",e)}})()},[]),(0,r.useEffect)(()=>{sessionStorage.setItem("macos-folder-positions",JSON.stringify(y))},[y]);let w=(0,r.useCallback)(e=>{v(t=>t.map(t=>t.id===e?{...t,isDragging:!0}:{...t,isDragging:!1}))},[]),j=(0,r.useCallback)((e,t)=>{v(a=>a.map(a=>{if(a.id===e){let e=window.innerWidth-100-50,i=window.innerHeight-100-50,r=a.position.x+t.offset.x,n=a.position.y+t.offset.y;return 0!==t.velocity.x&&(r+=.1*t.velocity.x),0!==t.velocity.y&&(n+=.1*t.velocity.y),r=Math.max(50,Math.min(r,e)),n=Math.max(50,Math.min(n,i)),{...a,position:{x:Math.round(r),y:Math.round(n)},isDragging:!1}}return a}))},[]),N=(0,r.useCallback)(e=>{h(e),b(!1),sessionStorage.setItem("macos-current-wallpaper",e)},[]);(0,r.useEffect)(()=>{{let e=sessionStorage.getItem("macos-current-wallpaper");e&&h(e)}},[]);let k=(0,r.useCallback)(e=>{if(a.find(t=>t.id===e.id)){l(t=>t.map(t=>t.id===e.id?{...t,zIndex:d,isMinimized:!1}:t)),c(e=>e+1);return}if("project"===e.type&&e.project)return void M(e.project);if("special"===e.type&&e.projects){if("more-projects"===e.id)return void window.open("https://github.com/nrenx","_blank");let t={id:e.id,title:e.name,content:(0,i.jsxs)("div",{className:"p-6 h-full overflow-auto",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:e.name}),(0,i.jsx)("div",{className:"grid gap-4",children:e.projects.map(e=>(0,i.jsxs)(s.P.div,{className:"p-4 bg-muted/50 rounded-lg border border-border/50 hover:border-primary/30 transition-colors cursor-pointer",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>M(e),children:[(0,i.jsx)("h4",{className:"font-medium text-foreground mb-2",children:e.title}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:e.description}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.technologies.slice(0,3).map(e=>(0,i.jsx)("span",{className:"px-2 py-1 bg-primary/10 text-primary text-xs rounded-md",children:e},e)),e.technologies.length>3&&(0,i.jsxs)("span",{className:"px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md",children:["+",e.technologies.length-3," more"]})]})]},e.id))})]}),position:{x:200+100*Math.random(),y:100+50*Math.random()},size:{width:500,height:400},isMinimized:!1,isMaximized:!1,zIndex:d};l(e=>[...e,t]),c(e=>e+1)}},[a,d]),M=(0,r.useCallback)(e=>{if(a.find(t=>t.id===e.id)){l(t=>t.map(t=>t.id===e.id?{...t,zIndex:d,isMinimized:!1}:t)),c(e=>e+1);return}let t={id:e.id,title:e.title,content:(0,i.jsxs)("div",{className:"p-6 h-full overflow-auto",children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-foreground mb-2",children:e.title}),(0,i.jsx)("p",{className:"text-muted-foreground",children:e.longDescription})]}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-foreground mb-2",children:"Technologies"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:e.technologies.map(e=>(0,i.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary text-sm rounded-md",children:e},e))})]}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-foreground mb-2",children:"Key Features"}),(0,i.jsx)("ul",{className:"space-y-1",children:e.features.map((e,t)=>(0,i.jsxs)("li",{className:"text-sm text-muted-foreground flex items-start gap-2",children:[(0,i.jsx)("span",{className:"w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"}),e]},t))})]}),(0,i.jsxs)("div",{className:"flex gap-3 pt-4 border-t border-border/50",children:[e.githubUrl&&(0,i.jsx)("a",{href:e.githubUrl,target:"_blank",rel:"noopener noreferrer",className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors",children:"View Code"}),e.liveUrl&&(0,i.jsx)("a",{href:e.liveUrl,target:"_blank",rel:"noopener noreferrer",className:"px-4 py-2 border border-border text-foreground rounded-lg text-sm font-medium hover:bg-muted/50 transition-colors",children:"Live Demo"})]})]}),position:{x:250+100*Math.random(),y:150+50*Math.random()},size:{width:600,height:500},isMinimized:!1,isMaximized:!1,zIndex:d};l(e=>[...e,t]),c(e=>e+1)},[a,d]),S=(0,r.useCallback)(e=>{l(t=>t.filter(t=>t.id!==e))},[]),I=(0,r.useCallback)(e=>{l(t=>t.map(t=>t.id===e?{...t,isMinimized:!0}:t))},[]),C=(0,r.useCallback)(e=>{l(t=>t.map(t=>t.id===e?{...t,isMaximized:!t.isMaximized,position:t.isMaximized?t.position:{x:0,y:0},size:t.isMaximized?t.size:{width:window.innerWidth,height:window.innerHeight-100}}:t))},[]),z=(0,r.useCallback)((e,t)=>{l(a=>a.map(a=>a.id===e?{...a,position:t}:a))},[]),T=(0,r.useCallback)(e=>{l(t=>t.map(t=>t.id===e?{...t,zIndex:d}:t)),c(e=>e+1)},[d]);return(0,i.jsxs)("div",{className:(0,p.cn)("relative w-full h-full overflow-hidden",t),children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat transition-all duration-500",style:{backgroundImage:"url('".concat(m,"')")},children:(0,i.jsx)("div",{className:"absolute inset-0 bg-black/10"})}),(0,i.jsx)("div",{className:"absolute inset-0 p-8",children:y.map(e=>(0,i.jsxs)(s.P.div,{className:"absolute flex flex-col items-center cursor-pointer group select-none hover:cursor-grab active:cursor-grabbing",style:{left:e.position.x,top:e.position.y},drag:!0,dragMomentum:!0,dragElastic:.1,dragTransition:{bounceStiffness:600,bounceDamping:20,power:.3,timeConstant:200},dragConstraints:{left:50,right:window.innerWidth-150,top:50,bottom:window.innerHeight-150},onDragStart:()=>w(e.id),onDragEnd:(t,a)=>j(e.id,a),whileHover:{scale:1.05,transition:{type:"spring",stiffness:400,damping:25}},whileTap:{scale:.95,transition:{type:"spring",stiffness:600,damping:30}},whileDrag:{scale:1.1,zIndex:1e3,boxShadow:"0 15px 40px rgba(0, 0, 0, 0.4)",rotate:[0,1,-1,0],transition:{type:"spring",stiffness:500,damping:25,rotate:{duration:.2,repeat:1/0,repeatType:"reverse"}}},onDoubleClick:()=>k(e),layout:!0,transition:{type:"spring",stiffness:400,damping:25,mass:.8,layout:{type:"spring",stiffness:500,damping:30,mass:.8}},children:[(0,i.jsxs)(s.P.div,{className:"w-16 h-16 mb-2 relative",animate:{rotateY:0,rotateX:0},whileHover:{rotateY:5,rotateX:5,transition:{type:"spring",stiffness:300,damping:20}},whileDrag:{rotateY:10,rotateX:10,transition:{type:"spring",stiffness:400,damping:25}},children:[(0,i.jsx)(P,{className:"w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-all duration-200"}),(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg transition-all duration-200 group-hover:from-yellow-200/30 group-hover:to-orange-300/30"})]}),(0,i.jsx)("span",{className:"text-white text-sm font-medium text-center drop-shadow-md max-w-20 leading-tight",children:e.name})]},e.id))}),(0,i.jsx)(n.N,{children:a.map(e=>(0,i.jsx)(A,{id:e.id,title:e.title,position:e.position,size:e.size,isMinimized:e.isMinimized,isMaximized:e.isMaximized,zIndex:e.zIndex,onClose:()=>S(e.id),onMinimize:()=>I(e.id),onMaximize:()=>C(e.id),onPositionChange:t=>z(e.id,t),onFocus:()=>T(e.id),children:e.content},e.id))}),(0,i.jsx)(n.N,{children:x&&(0,i.jsx)(A,{id:"wallpaper-selector",title:"Change Desktop Wallpaper",position:{x:window.innerWidth<768?20:300,y:window.innerWidth<768?50:150},size:{width:window.innerWidth<768?window.innerWidth-40:600,height:window.innerWidth<768?window.innerHeight-100:400},isMinimized:!1,isMaximized:!1,zIndex:d+1,onClose:()=>b(!1),onMinimize:()=>b(!1),onMaximize:()=>{},onPositionChange:()=>{},onFocus:()=>{},children:(0,i.jsxs)("div",{className:"p-4 md:p-6 h-full",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4 text-foreground",children:"Choose a wallpaper"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 h-full overflow-auto",children:u.map(e=>(0,i.jsxs)(s.P.div,{className:(0,p.cn)("relative aspect-video rounded-lg overflow-hidden cursor-pointer border-2 transition-all",m===e.path?"border-primary shadow-lg":"border-border/50 hover:border-primary/50"),onClick:()=>N(e.path),whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,i.jsx)("div",{className:"w-full h-full bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('".concat(e.path,"')")}}),(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"}),(0,i.jsx)("div",{className:"absolute bottom-2 left-2 right-2",children:(0,i.jsx)("p",{className:"text-white text-sm font-medium truncate",children:e.name})}),m===e.path&&(0,i.jsx)("div",{className:"absolute top-2 right-2",children:(0,i.jsx)("div",{className:"w-6 h-6 bg-primary rounded-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})})]},e.id))})]})})}),(0,i.jsx)(D,{openWindows:a,onWindowRestore:e=>{l(t=>t.map(t=>t.id===e?{...t,isMinimized:!1,zIndex:d}:t)),c(e=>e+1)},onWallpaperClick:()=>b(!0)})]})}function L(e){let{className:t}=e,a={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,i.jsxs)("section",{id:"projects",className:(0,p.cn)("min-h-screen py-20 bg-muted/20 relative overflow-hidden",t),children:[(0,i.jsx)("div",{className:"absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-primary/5 to-transparent"}),(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,i.jsxs)(s.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:[(0,i.jsxs)(s.P.div,{variants:a,className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-foreground mb-4",children:"Projects"}),(0,i.jsx)("div",{className:"w-20 h-1 bg-primary mx-auto rounded-full mb-6"}),(0,i.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Explore my projects through an interactive macOS-style interface. Click on folders to discover different categories of work."})]}),(0,i.jsx)(s.P.div,{variants:a,className:"macos-interface-container",children:(0,i.jsx)("div",{className:"relative w-full h-[600px] lg:h-[700px] rounded-2xl overflow-hidden shadow-2xl",children:(0,i.jsx)(E,{})})}),(0,i.jsx)(s.P.div,{variants:a,className:"grid grid-cols-2 lg:grid-cols-4 gap-6 mt-12",children:[{label:"Projects Completed",value:"10+"},{label:"Technologies Used",value:"15+"},{label:"Client Satisfaction",value:"100%"},{label:"Years Experience",value:"2+"}].map((e,t)=>(0,i.jsxs)(s.P.div,{className:"text-center p-6 bg-card/50 border border-border/50 rounded-lg",whileHover:{scale:1.05},transition:{duration:.2},children:[(0,i.jsx)("div",{className:"text-2xl lg:text-3xl font-bold text-primary mb-2",children:e.value}),(0,i.jsx)("div",{className:"text-sm text-muted-foreground",children:e.label})]},t))})]})})]})}H.filter(e=>"web"===e.category),H.filter(e=>"mobile"===e.category),H.filter(e=>"other"===e.category),H.slice(0,3),H.sort((e,t)=>new Date(t.startDate).getTime()-new Date(e.startDate).getTime()).slice(0,4);let B=(0,d.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]),O=(0,d.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),R=(0,d.A)("hand-heart",[["path",{d:"M11 14h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 16",key:"1ifwr1"}],["path",{d:"m7 20 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9",key:"17abbs"}],["path",{d:"m2 15 6 6",key:"10dquu"}],["path",{d:"M19.5 8.5c.7-.7 1.5-1.6 1.5-2.7A2.73 2.73 0 0 0 16 4a2.78 2.78 0 0 0-5 1.8c0 1.2.8 2 1.5 2.8L16 12Z",key:"1h3036"}]]);function W(e){let{className:t}=e,a=[{title:"Critical Thinking & Problem Solving",provider:"LinkedIn Learning",year:"2023",icon:N},{title:"Python Programming",provider:"Coursera",year:"2022",icon:m},{title:"Web Development Fundamentals",provider:"Udemy",year:"2023",icon:m}],r=[{title:"Participated in college-level coding competitions (2022-2023)",icon:B},{title:"Member of the Computer Science Club at NBKRIST College",icon:O},{title:"Volunteer for technical events at department symposiums",icon:R}],n={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}},o={hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.5}}};return(0,i.jsxs)("section",{id:"experience",className:(0,p.cn)("min-h-screen py-20 bg-background relative overflow-hidden",t),children:[(0,i.jsx)("div",{className:"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/3 to-transparent"}),(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,i.jsxs)(s.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:[(0,i.jsxs)(s.P.div,{variants:n,className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-foreground mb-4",children:"Experience & Certifications"}),(0,i.jsx)("div",{className:"w-20 h-1 bg-primary mx-auto rounded-full"})]}),(0,i.jsxs)("div",{className:"space-y-16",children:[(0,i.jsxs)(s.P.div,{variants:n,children:[(0,i.jsxs)("h3",{className:"text-2xl font-semibold text-foreground mb-8 flex items-center gap-2",children:[(0,i.jsx)(N,{className:"w-6 h-6 text-primary"}),"Certifications"]}),(0,i.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map((e,t)=>{let a=e.icon;return(0,i.jsx)(s.P.div,{className:"certification-card group",variants:o,whileHover:{scale:1.05,transition:{duration:.2}},whileTap:{scale:.98},children:(0,i.jsx)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6 h-full transition-all duration-300 hover:border-primary/30 hover:shadow-lg",children:(0,i.jsxs)("div",{className:"flex items-start gap-4",children:[(0,i.jsx)("div",{className:"certification-icon flex-shrink-0",children:(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors",children:(0,i.jsx)(a,{className:"w-6 h-6 text-primary"})})}),(0,i.jsxs)("div",{className:"certification-details flex-1",children:[(0,i.jsx)("h4",{className:"font-semibold text-foreground mb-2 leading-tight",children:e.title}),(0,i.jsxs)("p",{className:"text-muted-foreground text-sm",children:[e.provider," (",e.year,")"]})]})]})})},t)})})]}),(0,i.jsxs)(s.P.div,{variants:n,children:[(0,i.jsxs)("h3",{className:"text-2xl font-semibold text-foreground mb-8 flex items-center gap-2",children:[(0,i.jsx)(O,{className:"w-6 h-6 text-primary"}),"Extracurricular Activities"]}),(0,i.jsx)("div",{className:"space-y-4",children:r.map((e,t)=>{let a=e.icon;return(0,i.jsx)(s.P.div,{className:"activity-item group",variants:o,whileHover:{x:4,transition:{duration:.2}},children:(0,i.jsxs)("div",{className:"flex items-center gap-4 p-4 rounded-lg bg-card/30 border border-border/30 hover:border-primary/30 hover:bg-card/50 transition-all duration-300",children:[(0,i.jsx)("div",{className:"activity-icon flex-shrink-0",children:(0,i.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors",children:(0,i.jsx)(a,{className:"w-5 h-5 text-primary"})})}),(0,i.jsx)("span",{className:"text-foreground group-hover:text-foreground/90 transition-colors",children:e.title})]})},t)})})]})]})]})})]})}let F=(0,d.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),G=(0,d.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),_=(0,d.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),U=(0,d.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);function q(e){let{className:t}=e,[a,n]=(0,r.useState)({name:"",email:"",subject:"",message:""}),[l,d]=(0,r.useState)({}),[c,m]=(0,r.useState)(!1),[h,u]=(0,r.useState)("idle"),y=()=>{let e={};return a.name.trim()?a.name.length<o.cG.validation.minNameLength&&(e.name="Name must be at least ".concat(o.cG.validation.minNameLength," characters")):e.name="Name is required",a.email.trim()?o.cG.validation.email.test(a.email)||(e.email="Please enter a valid email address"):e.email="Email is required",a.subject.trim()||(e.subject="Subject is required"),a.message.trim()?a.message.length<o.cG.validation.minMessageLength&&(e.message="Message must be at least ".concat(o.cG.validation.minMessageLength," characters")):e.message="Message is required",d(e),0===Object.keys(e).length},v=e=>{let{name:t,value:a}=e.target;n(e=>({...e,[t]:a})),l[t]&&d(e=>({...e,[t]:void 0}))},w=async e=>{if(e.preventDefault(),y()){m(!0),u("idle");try{await new Promise(e=>setTimeout(e,2e3)),u("success"),n({name:"",email:"",subject:"",message:""})}catch(e){u("error")}finally{m(!1)}}},j={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6}}};return(0,i.jsxs)("section",{id:"contact",className:(0,p.cn)("min-h-screen py-20 pb-32 bg-background relative overflow-hidden",t),children:[(0,i.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/5 via-transparent to-transparent"}),(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,i.jsxs)(s.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},children:[(0,i.jsxs)(s.P.div,{variants:j,className:"text-center mb-16",children:[(0,i.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,i.jsx)("span",{className:"bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent uppercase tracking-wide",children:"Got a Vision? Let's Bring It to Life!"})}),(0,i.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed",children:"I'm enthusiastic about collaborating on innovative projects. Let's connect and explore how we can bring your vision to life!"})]}),(0,i.jsxs)("div",{className:"grid lg:grid-cols-5 gap-12",children:[(0,i.jsx)(s.P.div,{variants:j,className:"lg:col-span-2",children:(0,i.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-8 h-fit",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-6",children:"Contact Information"}),(0,i.jsx)("div",{className:"space-y-6",children:[{icon:f,label:"Email",value:"<EMAIL>",href:"mailto:<EMAIL>"},{icon:F,label:"Phone",value:"+91 ************",href:"tel:+917989976214"},{icon:G,label:"Location",value:"Edulapalli(Vi), Gudur(M), Tirupathi(D), Andhra Pradesh, 524409",href:null}].map((e,t)=>{let a=e.icon,r=(0,i.jsxs)("div",{className:"flex items-start gap-4 group",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors",children:(0,i.jsx)(a,{className:"w-5 h-5 text-primary"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:e.label}),(0,i.jsx)("p",{className:"text-foreground group-hover:text-primary transition-colors",children:e.value})]})]});return e.href?(0,i.jsx)(s.P.a,{href:e.href,className:"block hover:scale-105 transition-transform duration-200",whileHover:{x:4},children:r},t):(0,i.jsx)(s.P.div,{className:"block",children:r},t)})}),(0,i.jsxs)("div",{className:"mt-8 pt-6 border-t border-border/50",children:[(0,i.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Follow me on"}),(0,i.jsx)("div",{className:"flex gap-3",children:[{icon:g,label:"GitHub",href:"https://github.com/nrenx"},{icon:x,label:"LinkedIn",href:"https://linkedin.com/in/bollineninarendrachowdary"},{icon:b,label:"Twitter",href:"https://x.com/___CHOWDARY___"}].map((e,t)=>{let a=e.icon;return(0,i.jsx)(s.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-muted/50 rounded-lg flex items-center justify-center hover:bg-primary/20 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.95},"aria-label":e.label,children:(0,i.jsx)(a,{className:"w-5 h-5 text-muted-foreground hover:text-primary transition-colors"})},t)})})]}),(0,i.jsx)("div",{className:"mt-6",children:(0,i.jsx)(s.P.button,{onClick:()=>{window.open("/assets/resume/resume.pdf","_blank")},className:"w-full bg-muted/10 border border-primary/20 text-primary rounded-lg font-medium px-6 py-3 transition-all duration-300 hover:shadow-lg hover:bg-primary hover:text-primary-foreground group",whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,i.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(_,{className:"w-4 h-4"}),"View Resume"]})})})]})}),(0,i.jsx)(s.P.div,{variants:j,className:"lg:col-span-3",children:(0,i.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-8",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-6",children:"Send me a message"}),(0,i.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid sm:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-foreground mb-2",children:"Name *"}),(0,i.jsx)("input",{type:"text",id:"name",name:"name",value:a.name,onChange:v,placeholder:o.cG.placeholders.name,className:(0,p.cn)("w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary",l.name?"border-red-500":"border-border")}),l.name&&(0,i.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l.name})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-2",children:"Email *"}),(0,i.jsx)("input",{type:"email",id:"email",name:"email",value:a.email,onChange:v,placeholder:o.cG.placeholders.email,className:(0,p.cn)("w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary",l.email?"border-red-500":"border-border")}),l.email&&(0,i.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l.email})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-foreground mb-2",children:"Subject *"}),(0,i.jsx)("input",{type:"text",id:"subject",name:"subject",value:a.subject,onChange:v,placeholder:o.cG.placeholders.subject,className:(0,p.cn)("w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary",l.subject?"border-red-500":"border-border")}),l.subject&&(0,i.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l.subject})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-foreground mb-2",children:"Message *"}),(0,i.jsx)("textarea",{id:"message",name:"message",rows:6,value:a.message,onChange:v,placeholder:o.cG.placeholders.message,className:(0,p.cn)("w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors resize-none","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary",l.message?"border-red-500":"border-border")}),l.message&&(0,i.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l.message})]}),(0,i.jsx)(s.P.button,{type:"submit",disabled:c,className:(0,p.cn)("w-full sm:w-auto px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium","flex items-center justify-center gap-2 transition-all duration-300","hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",c&&"animate-pulse"),whileHover:{scale:1.02},whileTap:{scale:.98},children:c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin"}),"Sending..."]}):(0,i.jsxs)(i.Fragment,{children:["Send Message",(0,i.jsx)(U,{className:"w-4 h-4"})]})}),"success"===h&&(0,i.jsx)(s.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-green-600 text-sm",children:"Thank you for your message! I'll get back to you soon."}),"error"===h&&(0,i.jsx)(s.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-red-600 text-sm",children:"Something went wrong. Please try again."})]})]})})]})]})})]})}var V=a(3587);function K(){let{showLanding:e,handleLandingComplete:t}=(0,V.H)();return(0,i.jsxs)("div",{className:"min-h-screen",children:[e&&(0,i.jsx)(l,{onComplete:t}),(0,i.jsxs)("div",{className:e?"opacity-0":"opacity-100 transition-opacity duration-1000",children:[(0,i.jsx)(v,{}),(0,i.jsx)(k,{}),(0,i.jsx)(L,{}),(0,i.jsx)(W,{}),(0,i.jsx)(q,{})]})]})}},8084:(e,t,a)=>{Promise.resolve().then(a.bind(a,6310))},9434:(e,t,a)=>{"use strict";a.d(t,{O:()=>o,cn:()=>s});var i=a(2596),r=a(9688),n=a(9509);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,i.$)(t))}function o(e){let t="true"===n.env.GITHUB_PAGES?"/portfilio":"",a=e.startsWith("/")?e.slice(1):e;return t?"".concat(t,"/").concat(a):"/".concat(a)}}},e=>{var t=t=>e(e.s=t);e.O(0,[43,26,441,684,358],()=>t(8084)),_N_E=e.O()}]);