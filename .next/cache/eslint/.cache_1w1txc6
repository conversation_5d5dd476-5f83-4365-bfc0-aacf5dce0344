[{"/Users/<USER>/development/portfilio/portfilio/src/app/layout.tsx": "1", "/Users/<USER>/development/portfilio/portfilio/src/app/page.tsx": "2", "/Users/<USER>/development/portfilio/portfilio/src/components/common/animated-logo.tsx": "3", "/Users/<USER>/development/portfilio/portfilio/src/components/common/social-icons.tsx": "4", "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/custom-cursor.tsx": "5", "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/macos-desktop.tsx": "6", "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/macos-dock.tsx": "7", "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/macos-window.tsx": "8", "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/particle-animation.tsx": "9", "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/scroll-indicator.tsx": "10", "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/theme-toggle.tsx": "11", "/Users/<USER>/development/portfilio/portfilio/src/components/layout/footer.tsx": "12", "/Users/<USER>/development/portfilio/portfilio/src/components/layout/layout.tsx": "13", "/Users/<USER>/development/portfilio/portfilio/src/components/layout/mobile-menu.tsx": "14", "/Users/<USER>/development/portfilio/portfilio/src/components/layout/navbar.tsx": "15", "/Users/<USER>/development/portfilio/portfilio/src/components/sections/about-section.tsx": "16", "/Users/<USER>/development/portfilio/portfilio/src/components/sections/contact-section.tsx": "17", "/Users/<USER>/development/portfilio/portfilio/src/components/sections/experience-section.tsx": "18", "/Users/<USER>/development/portfilio/portfilio/src/components/sections/hero-section.tsx": "19", "/Users/<USER>/development/portfilio/portfilio/src/components/sections/landing-screen.tsx": "20", "/Users/<USER>/development/portfilio/portfilio/src/components/sections/projects-section.tsx": "21", "/Users/<USER>/development/portfilio/portfilio/src/contexts/landing-context.tsx": "22", "/Users/<USER>/development/portfilio/portfilio/src/contexts/theme-context.tsx": "23", "/Users/<USER>/development/portfilio/portfilio/src/data/projects.ts": "24", "/Users/<USER>/development/portfilio/portfilio/src/lib/constants.ts": "25", "/Users/<USER>/development/portfilio/portfilio/src/lib/types.ts": "26", "/Users/<USER>/development/portfilio/portfilio/src/lib/utils.ts": "27"}, {"size": 2015, "mtime": 1748446614793, "results": "28", "hashOfConfig": "29"}, {"size": 1231, "mtime": 1748446614794, "results": "30", "hashOfConfig": "29"}, {"size": 3525, "mtime": 1748446614794, "results": "31", "hashOfConfig": "29"}, {"size": 2949, "mtime": 1748446614794, "results": "32", "hashOfConfig": "29"}, {"size": 2032, "mtime": 1748446614794, "results": "33", "hashOfConfig": "29"}, {"size": 24967, "mtime": 1748450667566, "results": "34", "hashOfConfig": "29"}, {"size": 7126, "mtime": 1748446614795, "results": "35", "hashOfConfig": "29"}, {"size": 5843, "mtime": 1748446614796, "results": "36", "hashOfConfig": "29"}, {"size": 3073, "mtime": 1748450696824, "results": "37", "hashOfConfig": "29"}, {"size": 3793, "mtime": 1748446614797, "results": "38", "hashOfConfig": "29"}, {"size": 12726, "mtime": 1748446614798, "results": "39", "hashOfConfig": "29"}, {"size": 1488, "mtime": 1748446614798, "results": "40", "hashOfConfig": "29"}, {"size": 1501, "mtime": 1748446614798, "results": "41", "hashOfConfig": "29"}, {"size": 5985, "mtime": 1748446614798, "results": "42", "hashOfConfig": "29"}, {"size": 6817, "mtime": 1748446614798, "results": "43", "hashOfConfig": "29"}, {"size": 7289, "mtime": 1748446614799, "results": "44", "hashOfConfig": "29"}, {"size": 16054, "mtime": 1748450362574, "results": "45", "hashOfConfig": "29"}, {"size": 6499, "mtime": 1748446614799, "results": "46", "hashOfConfig": "29"}, {"size": 10333, "mtime": 1748446932690, "results": "47", "hashOfConfig": "29"}, {"size": 5013, "mtime": 1748446614800, "results": "48", "hashOfConfig": "29"}, {"size": 3293, "mtime": 1748446614800, "results": "49", "hashOfConfig": "29"}, {"size": 1216, "mtime": 1748446614800, "results": "50", "hashOfConfig": "29"}, {"size": 2527, "mtime": 1748446614800, "results": "51", "hashOfConfig": "29"}, {"size": 6551, "mtime": 1748450743978, "results": "52", "hashOfConfig": "29"}, {"size": 7385, "mtime": 1748451090267, "results": "53", "hashOfConfig": "29"}, {"size": 4949, "mtime": 1748446614801, "results": "54", "hashOfConfig": "29"}, {"size": 600, "mtime": 1748451104644, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k8itil", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/development/portfilio/portfilio/src/app/layout.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/app/page.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/common/animated-logo.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/common/social-icons.tsx", ["137"], [], "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/custom-cursor.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/macos-desktop.tsx", ["138", "139", "140", "141", "142", "143", "144", "145"], [], "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/macos-dock.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/macos-window.tsx", ["146"], [], "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/particle-animation.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/scroll-indicator.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/interactive/theme-toggle.tsx", ["147"], [], "/Users/<USER>/development/portfilio/portfilio/src/components/layout/footer.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/layout/layout.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/layout/mobile-menu.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/layout/navbar.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/sections/about-section.tsx", ["148"], [], "/Users/<USER>/development/portfilio/portfilio/src/components/sections/contact-section.tsx", ["149", "150", "151", "152", "153"], [], "/Users/<USER>/development/portfilio/portfilio/src/components/sections/experience-section.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/sections/hero-section.tsx", ["154", "155", "156", "157"], [], "/Users/<USER>/development/portfilio/portfilio/src/components/sections/landing-screen.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/components/sections/projects-section.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/contexts/landing-context.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/contexts/theme-context.tsx", [], [], "/Users/<USER>/development/portfilio/portfilio/src/data/projects.ts", [], [], "/Users/<USER>/development/portfilio/portfilio/src/lib/constants.ts", [], [], "/Users/<USER>/development/portfilio/portfilio/src/lib/types.ts", ["158"], [], "/Users/<USER>/development/portfilio/portfilio/src/lib/utils.ts", [], [], {"ruleId": "159", "severity": 1, "message": "160", "line": 49, "column": 32, "nodeType": null, "messageId": "161", "endLine": 49, "endColumn": 37}, {"ruleId": "159", "severity": 1, "message": "162", "line": 5, "column": 18, "nodeType": null, "messageId": "161", "endLine": 5, "endColumn": 19}, {"ruleId": "159", "severity": 1, "message": "163", "line": 5, "column": 21, "nodeType": null, "messageId": "161", "endLine": 5, "endColumn": 26}, {"ruleId": "159", "severity": 1, "message": "164", "line": 5, "column": 28, "nodeType": null, "messageId": "161", "endLine": 5, "endColumn": 34}, {"ruleId": "165", "severity": 1, "message": "166", "line": 30, "column": 13, "nodeType": "167", "messageId": "168", "endLine": 30, "endColumn": 16, "suggestions": "169"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 31, "column": 14, "nodeType": "167", "messageId": "168", "endLine": 31, "endColumn": 17, "suggestions": "170"}, {"ruleId": "159", "severity": 1, "message": "171", "line": 172, "column": 18, "nodeType": null, "messageId": "161", "endLine": 172, "endColumn": 19}, {"ruleId": "172", "severity": 1, "message": "173", "line": 361, "column": 6, "nodeType": "174", "endLine": 361, "endColumn": 31, "suggestions": "175"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 363, "column": 51, "nodeType": "167", "messageId": "168", "endLine": 363, "endColumn": 54, "suggestions": "176"}, {"ruleId": "159", "severity": 1, "message": "177", "line": 26, "column": 3, "nodeType": null, "messageId": "161", "endLine": 26, "endColumn": 5}, {"ruleId": "159", "severity": 1, "message": "178", "line": 4, "column": 18, "nodeType": null, "messageId": "161", "endLine": 4, "endColumn": 33}, {"ruleId": "179", "severity": 1, "message": "180", "line": 128, "column": 46, "nodeType": "181", "messageId": "182", "suggestions": "183"}, {"ruleId": "159", "severity": 1, "message": "184", "line": 139, "column": 14, "nodeType": null, "messageId": "161", "endLine": 139, "endColumn": 19}, {"ruleId": "179", "severity": 1, "message": "180", "line": 189, "column": 34, "nodeType": "181", "messageId": "182", "suggestions": "185"}, {"ruleId": "179", "severity": 1, "message": "180", "line": 193, "column": 16, "nodeType": "181", "messageId": "182", "suggestions": "186"}, {"ruleId": "179", "severity": 1, "message": "180", "line": 193, "column": 79, "nodeType": "181", "messageId": "182", "suggestions": "187"}, {"ruleId": "179", "severity": 1, "message": "180", "line": 410, "column": 52, "nodeType": "181", "messageId": "182", "suggestions": "188"}, {"ruleId": "159", "severity": 1, "message": "189", "line": 5, "column": 28, "nodeType": null, "messageId": "161", "endLine": 5, "endColumn": 34}, {"ruleId": "159", "severity": 1, "message": "190", "line": 5, "column": 36, "nodeType": null, "messageId": "161", "endLine": 5, "endColumn": 44}, {"ruleId": "159", "severity": 1, "message": "191", "line": 5, "column": 46, "nodeType": null, "messageId": "161", "endLine": 5, "endColumn": 53}, {"ruleId": "179", "severity": 1, "message": "180", "line": 145, "column": 20, "nodeType": "181", "messageId": "182", "suggestions": "192"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 189, "column": 34, "nodeType": "167", "messageId": "168", "endLine": 189, "endColumn": 37, "suggestions": "193"}, "@typescript-eslint/no-unused-vars", "'index' is defined but never used.", "unusedVar", "'X' is defined but never used.", "'Minus' is defined but never used.", "'Square' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["194", "195"], ["196", "197"], "'e' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'openProjectWindow'. Either include it or remove the dependency array.", "ArrayExpression", ["198"], ["199", "200"], "'id' is defined but never used.", "'AnimatePresence' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["201", "202", "203", "204"], "'error' is defined but never used.", ["205", "206", "207", "208"], ["209", "210", "211", "212"], ["213", "214", "215", "216"], ["217", "218", "219", "220"], "'Github' is defined but never used.", "'Linkedin' is defined but never used.", "'Twitter' is defined but never used.", ["221", "222", "223", "224"], ["225", "226"], {"messageId": "227", "fix": "228", "desc": "229"}, {"messageId": "230", "fix": "231", "desc": "232"}, {"messageId": "227", "fix": "233", "desc": "229"}, {"messageId": "230", "fix": "234", "desc": "232"}, {"desc": "235", "fix": "236"}, {"messageId": "227", "fix": "237", "desc": "229"}, {"messageId": "230", "fix": "238", "desc": "232"}, {"messageId": "239", "data": "240", "fix": "241", "desc": "242"}, {"messageId": "239", "data": "243", "fix": "244", "desc": "245"}, {"messageId": "239", "data": "246", "fix": "247", "desc": "248"}, {"messageId": "239", "data": "249", "fix": "250", "desc": "251"}, {"messageId": "239", "data": "252", "fix": "253", "desc": "242"}, {"messageId": "239", "data": "254", "fix": "255", "desc": "245"}, {"messageId": "239", "data": "256", "fix": "257", "desc": "248"}, {"messageId": "239", "data": "258", "fix": "259", "desc": "251"}, {"messageId": "239", "data": "260", "fix": "261", "desc": "242"}, {"messageId": "239", "data": "262", "fix": "263", "desc": "245"}, {"messageId": "239", "data": "264", "fix": "265", "desc": "248"}, {"messageId": "239", "data": "266", "fix": "267", "desc": "251"}, {"messageId": "239", "data": "268", "fix": "269", "desc": "242"}, {"messageId": "239", "data": "270", "fix": "271", "desc": "245"}, {"messageId": "239", "data": "272", "fix": "273", "desc": "248"}, {"messageId": "239", "data": "274", "fix": "275", "desc": "251"}, {"messageId": "239", "data": "276", "fix": "277", "desc": "242"}, {"messageId": "239", "data": "278", "fix": "279", "desc": "245"}, {"messageId": "239", "data": "280", "fix": "281", "desc": "248"}, {"messageId": "239", "data": "282", "fix": "283", "desc": "251"}, {"messageId": "239", "data": "284", "fix": "285", "desc": "242"}, {"messageId": "239", "data": "286", "fix": "287", "desc": "245"}, {"messageId": "239", "data": "288", "fix": "289", "desc": "248"}, {"messageId": "239", "data": "290", "fix": "291", "desc": "251"}, {"messageId": "227", "fix": "292", "desc": "229"}, {"messageId": "230", "fix": "293", "desc": "232"}, "suggestUnknown", {"range": "294", "text": "295"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "296", "text": "297"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "298", "text": "295"}, {"range": "299", "text": "297"}, "Update the dependencies array to be: [openWindows, nextZIndex, openProjectWindow]", {"range": "300", "text": "301"}, {"range": "302", "text": "295"}, {"range": "303", "text": "297"}, "replaceWithAlt", {"alt": "304"}, {"range": "305", "text": "306"}, "Replace with `&apos;`.", {"alt": "307"}, {"range": "308", "text": "309"}, "Replace with `&lsquo;`.", {"alt": "310"}, {"range": "311", "text": "312"}, "Replace with `&#39;`.", {"alt": "313"}, {"range": "314", "text": "315"}, "Replace with `&rsquo;`.", {"alt": "304"}, {"range": "316", "text": "317"}, {"alt": "307"}, {"range": "318", "text": "319"}, {"alt": "310"}, {"range": "320", "text": "321"}, {"alt": "313"}, {"range": "322", "text": "323"}, {"alt": "304"}, {"range": "324", "text": "325"}, {"alt": "307"}, {"range": "326", "text": "327"}, {"alt": "310"}, {"range": "328", "text": "329"}, {"alt": "313"}, {"range": "330", "text": "331"}, {"alt": "304"}, {"range": "332", "text": "333"}, {"alt": "307"}, {"range": "334", "text": "335"}, {"alt": "310"}, {"range": "336", "text": "337"}, {"alt": "313"}, {"range": "338", "text": "339"}, {"alt": "304"}, {"range": "340", "text": "341"}, {"alt": "307"}, {"range": "342", "text": "343"}, {"alt": "310"}, {"range": "344", "text": "345"}, {"alt": "313"}, {"range": "346", "text": "347"}, {"alt": "304"}, {"range": "348", "text": "349"}, {"alt": "307"}, {"range": "350", "text": "351"}, {"alt": "310"}, {"range": "352", "text": "353"}, {"alt": "313"}, {"range": "354", "text": "355"}, {"range": "356", "text": "295"}, {"range": "357", "text": "297"}, [771, 774], "unknown", [771, 774], "never", [830, 833], [830, 833], [12721, 12746], "[openWindows, nextZIndex, openProjectWindow]", [12800, 12803], [12800, 12803], "&apos;", [3314, 3841], "\n                  Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. \n                  I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, \n                  and automation workflows. I&apos;m seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, \n                  and mobile app creation to deliver cost-effective and innovative solutions.\n                ", "&lsquo;", [3314, 3841], "\n                  Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. \n                  I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, \n                  and automation workflows. I&lsquo;m seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, \n                  and mobile app creation to deliver cost-effective and innovative solutions.\n                ", "&#39;", [3314, 3841], "\n                  Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. \n                  I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, \n                  and automation workflows. I&#39;m seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, \n                  and mobile app creation to deliver cost-effective and innovative solutions.\n                ", "&rsquo;", [3314, 3841], "\n                  Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. \n                  I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, \n                  and automation workflows. I&rsquo;m seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, \n                  and mobile app creation to deliver cost-effective and innovative solutions.\n                ", [5085, 5154], "\n                Got a Vision? Let&apos;s Bring It to Life!\n              ", [5085, 5154], "\n                Got a Vision? Let&lsquo;s Bring It to Life!\n              ", [5085, 5154], "\n                Got a Vision? Let&#39;s Bring It to Life!\n              ", [5085, 5154], "\n                Got a Vision? Let&rsquo;s Bring It to Life!\n              ", [5271, 5423], "\n              I&apos;m enthusiastic about collaborating on innovative projects. Let's connect and explore how we can bring your vision to life!\n            ", [5271, 5423], "\n              I&lsquo;m enthusiastic about collaborating on innovative projects. Let's connect and explore how we can bring your vision to life!\n            ", [5271, 5423], "\n              I&#39;m enthusiastic about collaborating on innovative projects. Let's connect and explore how we can bring your vision to life!\n            ", [5271, 5423], "\n              I&rsquo;m enthusiastic about collaborating on innovative projects. Let's connect and explore how we can bring your vision to life!\n            ", [5271, 5423], "\n              I'm enthusiastic about collaborating on innovative projects. Let&apos;s connect and explore how we can bring your vision to life!\n            ", [5271, 5423], "\n              I'm enthusiastic about collaborating on innovative projects. Let&lsquo;s connect and explore how we can bring your vision to life!\n            ", [5271, 5423], "\n              I'm enthusiastic about collaborating on innovative projects. Let&#39;s connect and explore how we can bring your vision to life!\n            ", [5271, 5423], "\n              I'm enthusiastic about collaborating on innovative projects. Let&rsquo;s connect and explore how we can bring your vision to life!\n            ", [15397, 15495], "\n                      Thank you for your message! I&apos;ll get back to you soon.\n                    ", [15397, 15495], "\n                      Thank you for your message! I&lsquo;ll get back to you soon.\n                    ", [15397, 15495], "\n                      Thank you for your message! I&#39;ll get back to you soon.\n                    ", [15397, 15495], "\n                      Thank you for your message! I&rsquo;ll get back to you soon.\n                    ", [6347, 6378], "\n              <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>;m <PERSON><PERSON><PERSON>", [6347, 6378], "\n              <PERSON>, <PERSON>&lsquo;m <PERSON><PERSON><PERSON>", [6347, 6378], "\n              Hi, I&#39;m <PERSON><PERSON><PERSON>", [6347, 6378], "\n              Hi, I&rsquo;m <PERSON><PERSON><PERSON>", [3647, 3650], [3647, 3650]]