1:"$Sreact.fragment"
2:I[1654,["43","static/chunks/framer-motion-eaab7625ee3d9f1f.js","26","static/chunks/26-7965e5ed4c6ca054.js","177","static/chunks/app/layout-887b776b903c3aac.js"],"Layout"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[894,[],"ClientPageRoot"]
6:I[6310,["43","static/chunks/framer-motion-eaab7625ee3d9f1f.js","26","static/chunks/26-7965e5ed4c6ca054.js","974","static/chunks/app/page-faad5c5e26d3d80b.js"],"default"]
9:I[9665,[],"MetadataBoundary"]
b:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[9665,[],"ViewportBoundary"]
12:I[6614,[],""]
:HL["/portfilio/_next/static/media/0484562807a97172-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/portfilio/_next/static/media/4c285fdca692ea22-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/portfilio/_next/static/media/8888a3826f4a3af4-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/portfilio/_next/static/media/a34f9d1faa5f3315-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/portfilio/_next/static/media/b957ea75a84b6ea7-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/portfilio/_next/static/media/eafabf029ad39a43-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/portfilio/_next/static/css/f9019c3291a4513c.css","style"]
0:{"P":null,"b":"WW27sZyVZ2kmcnw2gbKiJ","p":"/portfilio","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/portfilio/_next/static/css/f9019c3291a4513c.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_dcbc8f __variable_0a793b font-inter antialiased","style":{"fontFamily":"var(--font-inter), var(--font-poppins), -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif"},"children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L5",null,{"Component":"$6","searchParams":{},"params":{},"promises":["$@7","$@8"]}],["$","$L9",null,{"children":"$La"}],null,["$","$Lb",null,{"children":["$Lc","$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","je2EMViDO-kCITFdInVyV",{"children":[["$","$L10",null,{"children":"$L11"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],null]}],false]],"m":"$undefined","G":["$12","$undefined"],"s":false,"S":true}
13:"$Sreact.suspense"
14:I[4911,[],"AsyncMetadata"]
7:{}
8:{}
a:["$","$13",null,{"fallback":null,"children":["$","$L14",null,{"promise":"$@15"}]}]
d:null
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:{"metadata":[["$","title","0",{"children":"Portfolio - Full Stack Developer"}],["$","meta","1",{"name":"description","content":"Professional portfolio showcasing full-stack development projects, skills, and experience."}],["$","meta","2",{"name":"author","content":"Your Name"}],["$","meta","3",{"name":"keywords","content":"full-stack developer,web development,react,next.js,typescript,portfolio,software engineer"}],["$","meta","4",{"name":"creator","content":"Your Name"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","7",{"name":"google-site-verification","content":"your-google-verification-code"}],["$","meta","8",{"property":"og:title","content":"Portfolio - Full Stack Developer"}],["$","meta","9",{"property":"og:description","content":"Professional portfolio showcasing full-stack development projects, skills, and experience."}],["$","meta","10",{"property":"og:url","content":"https://nrenx.github.io/portfilio/"}],["$","meta","11",{"property":"og:site_name","content":"Portfolio - Full Stack Developer"}],["$","meta","12",{"property":"og:locale","content":"en_US"}],["$","meta","13",{"property":"og:image","content":"https://nrenx.github.io/portfilio/assets/images/og-image.jpg"}],["$","meta","14",{"property":"og:image:width","content":"1200"}],["$","meta","15",{"property":"og:image:height","content":"630"}],["$","meta","16",{"property":"og:image:alt","content":"Portfolio - Full Stack Developer"}],["$","meta","17",{"property":"og:type","content":"website"}],["$","meta","18",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","19",{"name":"twitter:creator","content":"@___CHOWDARY___"}],["$","meta","20",{"name":"twitter:title","content":"Portfolio - Full Stack Developer"}],["$","meta","21",{"name":"twitter:description","content":"Professional portfolio showcasing full-stack development projects, skills, and experience."}],["$","meta","22",{"name":"twitter:image","content":"https://nrenx.github.io/portfilio/assets/images/og-image.jpg"}],["$","link","23",{"rel":"icon","href":"/portfilio/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
f:{"metadata":"$15:metadata","error":null,"digest":"$undefined"}
