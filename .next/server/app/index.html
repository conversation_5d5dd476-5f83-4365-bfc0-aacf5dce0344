<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/portfilio/_next/static/media/0484562807a97172-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/portfilio/_next/static/media/4c285fdca692ea22-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/portfilio/_next/static/media/8888a3826f4a3af4-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/portfilio/_next/static/media/a34f9d1faa5f3315-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/portfilio/_next/static/media/b957ea75a84b6ea7-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/portfilio/_next/static/media/eafabf029ad39a43-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/portfilio/_next/static/css/f9019c3291a4513c.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/portfilio/_next/static/chunks/webpack-b26f0683916655f0.js"/><script src="/portfilio/_next/static/chunks/4bd1b696-67284396f4fcf8b1.js" async=""></script><script src="/portfilio/_next/static/chunks/684-2afab52f367e8aeb.js" async=""></script><script src="/portfilio/_next/static/chunks/main-app-e0401eab94a6c70b.js" async=""></script><script src="/portfilio/_next/static/chunks/framer-motion-eaab7625ee3d9f1f.js" async=""></script><script src="/portfilio/_next/static/chunks/26-7965e5ed4c6ca054.js" async=""></script><script src="/portfilio/_next/static/chunks/app/layout-5f7ea02908601492.js" async=""></script><script src="/portfilio/_next/static/chunks/app/page-1175d4c8d8246d06.js" async=""></script><meta name="next-size-adjust" content=""/><title>Portfolio - Full Stack Developer</title><meta name="description" content="Professional portfolio showcasing full-stack development projects, skills, and experience."/><meta name="author" content="Your Name"/><meta name="keywords" content="full-stack developer,web development,react,next.js,typescript,portfolio,software engineer"/><meta name="creator" content="Your Name"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="Portfolio - Full Stack Developer"/><meta property="og:description" content="Professional portfolio showcasing full-stack development projects, skills, and experience."/><meta property="og:url" content="https://nrenx.github.io/portfilio/"/><meta property="og:site_name" content="Portfolio - Full Stack Developer"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="https://nrenx.github.io/portfilio/assets/images/og-image.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="Portfolio - Full Stack Developer"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@___CHOWDARY___"/><meta name="twitter:title" content="Portfolio - Full Stack Developer"/><meta name="twitter:description" content="Professional portfolio showcasing full-stack development projects, skills, and experience."/><meta name="twitter:image" content="https://nrenx.github.io/portfilio/assets/images/og-image.jpg"/><link rel="icon" href="/portfilio/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/portfilio/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_dcbc8f __variable_0a793b font-inter antialiased" style="font-family:var(--font-inter), var(--font-poppins), -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Open Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif"><div class="min-h-screen bg-background text-foreground"><div class="min-h-screen bg-background text-foreground transition-colors duration-300"><div class="
        fixed top-0 left-0 w-3 h-3 rounded-full pointer-events-none z-[9999]
        transform -translate-x-1/2 -translate-y-1/2 transition-transform duration-300 ease-out
        opacity-80
        bg-black shadow-[0_0_5px_rgba(0,0,0,0.5)]
      " style="display:block"></div><main class="relative z-10"><div class="min-h-screen"><div class="fixed inset-0 z-[2000] flex items-center justify-center overflow-hidden" style="background:linear-gradient(135deg, #000, #222);perspective:1000px;opacity:1;transform:none"><div class="absolute w-[200%] h-[200%] opacity-20" style="background:radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(0,0,0,0) 70%)"></div><div class="relative h-[120px] w-full text-center"><div class="absolute inset-0 flex items-center justify-center" style="opacity:0;transform:none"><h1 class="text-5xl md:text-6xl font-semibold text-white/95" style="text-shadow:0 4px 12px rgba(0, 0, 0, 0.3)">Hello</h1></div></div></div><div class="opacity-0"><section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden pt-16 lg:pt-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full"><div class="flex flex-col lg:flex-row items-center justify-between min-h-[calc(100vh-4rem)] lg:min-h-[calc(100vh-5rem)] gap-8"><div class="image-container flex-shrink-0 flex justify-center lg:justify-center order-1 lg:order-2" style="opacity:0;transform:scale(0.8)"><div class="morph-container group relative w-[300px] h-[300px] overflow-hidden bg-cover bg-no-repeat bg-center border-4 border-[#2d2e32]" style="border-radius:60% 40% 30% 70%/60% 30% 70% 40%;background-image:url(&#x27;data:image/svg+xml,%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20300%20300%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cdefs%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3ClinearGradient%20id%3D%22grad1%22%20x1%3D%220%25%22%20y1%3D%220%25%22%20x2%3D%22100%25%22%20y2%3D%22100%25%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cstop%20offset%3D%220%25%22%20style%3D%22stop-color%3A%23667eea%3Bstop-opacity%3A1%22%20%2F%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cstop%20offset%3D%22100%25%22%20style%3D%22stop-color%3A%23764ba2%3Bstop-opacity%3A1%22%20%2F%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C%2FlinearGradient%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C%2Fdefs%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Crect%20width%3D%22300%22%20height%3D%22300%22%20fill%3D%22url(%23grad1)%22%20%2F%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ccircle%20cx%3D%22150%22%20cy%3D%22120%22%20r%3D%2240%22%20fill%3D%22rgba(255%2C255%2C255%2C0.1)%22%20%2F%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cellipse%20cx%3D%22150%22%20cy%3D%22200%22%20rx%3D%2260%22%20ry%3D%2280%22%20fill%3D%22rgba(255%2C255%2C255%2C0.05)%22%20%2F%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ctext%20x%3D%22150%22%20y%3D%22160%22%20text-anchor%3D%22middle%22%20fill%3D%22white%22%20font-size%3D%2248%22%3E%F0%9F%91%A8%E2%80%8D%F0%9F%92%BB%3C%2Ftext%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C%2Fsvg%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20&#x27;)"><div class="background-image absolute top-0 left-0 w-full h-full object-cover z-[1] transition-opacity duration-500 ease-in-out group-hover:opacity-0" style="background-image:url(&#x27;/assets/images/Finding joy in the simplicity of the sea ............beach bridge ocean smile sunny monument collage sunset sunrise travelphotography travel.jpg&#x27;);background-size:cover;background-position:center;background-repeat:no-repeat"></div><div class="second-image absolute top-0 left-0 w-full h-full object-cover z-[2] opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100" style="background-image:url(&#x27;/assets/images/Finding paradise wherever the waves take me. . . . . . . . . . . . . . . .beachbound beachlife beach beachdreaming ocean paradise wavesfordays explore rainyday shorelineadventures seasideescape beach.jpg&#x27;);background-size:cover;background-position:center;background-repeat:no-repeat"></div></div></div><div class="text-container space-y-6 flex-1 max-w-2xl order-2 lg:order-1" style="opacity:0;transform:translateY(30px)"><div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm font-medium" style="opacity:0;transform:scale(0.8)"><span class="w-2 h-2 bg-green-500 rounded-full"></span>Available for work</div><h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight" style="opacity:0;transform:translateY(20px)">Hi, I&#x27;m Narendra<!-- --> <span class="inline-block">👋</span></h1><div class="bio-container space-y-3" style="opacity:0;transform:translateY(20px)"><h2 class="text-xl sm:text-2xl font-semibold text-foreground">No-Code Developer | AI Prompt Engineer | Mobile App Creator</h2><p class="text-lg text-muted-foreground leading-relaxed max-w-2xl">Recent Computer Science and Engineering graduate specializing in AI-assisted development and no-code solutions. I leverage modern AI tools to build efficient SaaS applications, mobile apps, and automation workflows. With expertise in cloud backends and API integration, I create cost-effective digital solutions with minimal traditional coding.</p></div><div class="button-container flex flex-col sm:flex-row gap-4 mt-4" style="opacity:0;transform:translateY(20px)"><button class="button get-in-touch group relative px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg" tabindex="0"><span class="relative z-10 flex items-center gap-2">Get in Touch<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 transition-transform group-hover:translate-x-1" aria-hidden="true"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></span><div class="absolute inset-0 bg-gradient-to-r from-primary to-primary/80" style="transform:translateX(-100%)"></div></button><button class="button view-projects group relative px-8 py-3 border border-border text-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-primary/50" tabindex="0"><span class="relative z-10 flex items-center gap-2">View Projects<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-4 h-4 transition-transform group-hover:rotate-12" aria-hidden="true"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg></span><div class="absolute inset-0 bg-muted/50" style="transform:translateX(-100%)"></div></button></div><div style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-4" style="opacity:0"><a href="https://github.com/nrenx" target="_blank" rel="noopener noreferrer" class="group relative p-3 rounded-full bg-muted/50 hover:bg-muted transition-all duration-300" aria-label="GitHub Profile" tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style="transform:none"></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors duration-300 relative z-10" aria-hidden="true"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg><div class="absolute -top-12 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap" style="opacity:0;transform:translateY(10px)">GitHub<div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover"></div></div></a><a href="https://linkedin.com/in/bollineninarendrachowdary" target="_blank" rel="noopener noreferrer" class="group relative p-3 rounded-full bg-muted/50 hover:bg-muted transition-all duration-300" aria-label="LinkedIn Profile" tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style="transform:none"></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors duration-300 relative z-10" aria-hidden="true"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg><div class="absolute -top-12 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap" style="opacity:0;transform:translateY(10px)">LinkedIn<div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover"></div></div></a><a href="https://x.com/___CHOWDARY___" target="_blank" rel="noopener noreferrer" class="group relative p-3 rounded-full bg-muted/50 hover:bg-muted transition-all duration-300" aria-label="Twitter Profile" tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style="transform:none"></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors duration-300 relative z-10" aria-hidden="true"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg><div class="absolute -top-12 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap" style="opacity:0;transform:translateY(10px)">Twitter<div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover"></div></div></a><a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" class="group relative p-3 rounded-full bg-muted/50 hover:bg-muted transition-all duration-300" aria-label="Send Email" tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style="transform:none"></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors duration-300 relative z-10" aria-hidden="true"><path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"></path><rect x="2" y="4" width="20" height="16" rx="2"></rect></svg><div class="absolute -top-12 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap" style="opacity:0;transform:translateY(10px)">Email<div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover"></div></div></a></div></div></div></div></div><div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center cursor-pointer z-10" tabindex="0" style="opacity:0;transform:translateY(20px)"><span class="text-sm text-muted-foreground mb-2 font-medium">Scroll Down</span><div class="relative"><div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-6 h-6 text-muted-foreground" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></div><div class="absolute top-0 left-0" style="margin-top:-8px"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-6 h-6 text-muted-foreground opacity-60" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></div></div><div class="absolute inset-0 rounded-full bg-primary/20 blur-xl opacity-0"></div></div></section><section id="about" class="min-h-screen py-20 bg-muted/20 relative overflow-hidden"><div class="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/5 to-transparent"></div><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"><div style="opacity:0"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-3xl sm:text-4xl font-bold text-foreground mb-4">About Me</h2><div class="w-20 h-1 bg-primary mx-auto rounded-full"></div></div><div class="grid lg:grid-cols-2 gap-12 lg:gap-16"><div class="space-y-8" style="opacity:0;transform:translateY(30px)"><div class="prose prose-lg dark:prose-invert max-w-none"><p class="text-lg text-muted-foreground leading-relaxed">Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, and automation workflows. I&#x27;m seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, and mobile app creation to deliver cost-effective and innovative solutions.</p></div><div class="education-container"><h3 class="text-2xl font-semibold text-foreground mb-6 flex items-center gap-2" style="opacity:0;transform:translateY(30px)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap w-6 h-6 text-primary" aria-hidden="true"><path d="M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z"></path><path d="M22 10v6"></path><path d="M6 12.5V16a6 3 0 0 0 12 0v-3.5"></path></svg>Education</h3><div class="space-y-6"><div class="education-item flex gap-4 p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><div class="education-year flex-shrink-0"><div class="flex items-center gap-2 text-sm font-medium text-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-4 h-4" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>Expected 2026</div></div><div class="education-details flex-1"><h4 class="font-semibold text-foreground mb-1">Bachelor of Engineering in Computer Science and Engineering</h4><p class="text-muted-foreground mb-1">NBKRIST College Autonomous</p><p class="text-sm text-muted-foreground">CGPA: 8.2 (Current)</p></div></div><div class="education-item flex gap-4 p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><div class="education-year flex-shrink-0"><div class="flex items-center gap-2 text-sm font-medium text-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-4 h-4" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>Graduated 2022</div></div><div class="education-details flex-1"><h4 class="font-semibold text-foreground mb-1">Intermediate</h4><p class="text-muted-foreground mb-1">Narayana Junior College, State Board</p><p class="text-sm text-muted-foreground">CGPA: 5.55</p></div></div><div class="education-item flex gap-4 p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><div class="education-year flex-shrink-0"><div class="flex items-center gap-2 text-sm font-medium text-primary"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-4 h-4" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>Graduated 2020</div></div><div class="education-details flex-1"><h4 class="font-semibold text-foreground mb-1">SSC</h4><p class="text-muted-foreground mb-1">Narayana EM High School, State Board</p><p class="text-sm text-muted-foreground">CGPA: 9.88</p></div></div></div></div></div><div class="skills-container" style="opacity:0;transform:translateY(30px)"><h3 class="text-2xl font-semibold text-foreground mb-6 flex items-center gap-2" style="opacity:0;transform:translateY(30px)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award w-6 h-6 text-primary" aria-hidden="true"><path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"></path><circle cx="12" cy="8" r="6"></circle></svg>Skills</h3><div class="skills-grid space-y-6"><div class="skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><h4 class="font-semibold text-foreground mb-3">No-Code/Low-Code</h4><ul class="skills-list space-y-2"><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>SaaS development using AI-assisted tools &amp; platforms</li></ul></div><div class="skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><h4 class="font-semibold text-foreground mb-3">Cloud &amp; Backend</h4><ul class="skills-list space-y-2"><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Supabase &amp; Firebase – auth, DB, storage</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>API Integration &amp; key management</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Cost-optimized usage of 3rd-party services</li></ul></div><div class="skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><h4 class="font-semibold text-foreground mb-3">Mobile Development</h4><ul class="skills-list space-y-2"><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Android &amp; iOS dev via AI tools</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Android Studio</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Xcode</li></ul></div><div class="skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><h4 class="font-semibold text-foreground mb-3">AI &amp; Automation</h4><ul class="skills-list space-y-2"><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>AI Prompt Engineering with low-iteration design</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Workflow automation using n8n</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Telegram bots for info delivery &amp; engagement</li></ul></div><div class="skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors" style="opacity:0;transform:translateY(30px)"><h4 class="font-semibold text-foreground mb-3">Web Development</h4><ul class="skills-list space-y-2"><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>HTML</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>CSS</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>JavaScript</li><li class="text-muted-foreground flex items-start gap-2"><span class="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></span>Basic front-end tasks</li></ul></div></div></div></div></div></div></section><section id="projects" class="min-h-screen py-20 bg-muted/20 relative overflow-hidden"><div class="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-primary/5 to-transparent"></div><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"><div style="opacity:0"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-3xl sm:text-4xl font-bold text-foreground mb-4">Projects</h2><div class="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div><p class="text-lg text-muted-foreground max-w-2xl mx-auto">Explore my projects through an interactive macOS-style interface. Click on folders to discover different categories of work.</p></div><div class="macos-interface-container" style="opacity:0;transform:translateY(30px)"><div class="relative w-full h-[600px] lg:h-[700px] rounded-2xl overflow-hidden shadow-2xl"><div class="relative w-full h-full overflow-hidden"><div class="absolute inset-0 bg-cover bg-center bg-no-repeat transition-all duration-500" style="background-image:url(&#x27;/assets/macOS-wallpaper/wallpaperflare.com_wallpaper (1).jpg&#x27;)"><div class="absolute inset-0 bg-black/10"></div></div><div class="absolute inset-0 p-8"><div class="absolute flex flex-col items-center cursor-pointer group select-none hover:cursor-grab active:cursor-grabbing" draggable="false" tabindex="0" style="left:100px;top:80px;-webkit-touch-callout:none;-webkit-user-select:none;user-select:none;touch-action:none"><div class="w-16 h-16 mb-2 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-all duration-200" aria-hidden="true"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path></svg><div class="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg transition-all duration-200 group-hover:from-yellow-200/30 group-hover:to-orange-300/30"></div></div><span class="text-white text-sm font-medium text-center drop-shadow-md max-w-20 leading-tight">Trade Book Ledge</span></div><div class="absolute flex flex-col items-center cursor-pointer group select-none hover:cursor-grab active:cursor-grabbing" draggable="false" tabindex="0" style="left:230px;top:80px;-webkit-touch-callout:none;-webkit-user-select:none;user-select:none;touch-action:none"><div class="w-16 h-16 mb-2 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-all duration-200" aria-hidden="true"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path></svg><div class="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg transition-all duration-200 group-hover:from-yellow-200/30 group-hover:to-orange-300/30"></div></div><span class="text-white text-sm font-medium text-center drop-shadow-md max-w-20 leading-tight">NBKRIST Student Portal</span></div><div class="absolute flex flex-col items-center cursor-pointer group select-none hover:cursor-grab active:cursor-grabbing" draggable="false" tabindex="0" style="left:100px;top:220px;-webkit-touch-callout:none;-webkit-user-select:none;user-select:none;touch-action:none"><div class="w-16 h-16 mb-2 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-all duration-200" aria-hidden="true"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path></svg><div class="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg transition-all duration-200 group-hover:from-yellow-200/30 group-hover:to-orange-300/30"></div></div><span class="text-white text-sm font-medium text-center drop-shadow-md max-w-20 leading-tight">AI Weather Reporter</span></div><div class="absolute flex flex-col items-center cursor-pointer group select-none hover:cursor-grab active:cursor-grabbing" draggable="false" tabindex="0" style="left:230px;top:220px;-webkit-touch-callout:none;-webkit-user-select:none;user-select:none;touch-action:none"><div class="w-16 h-16 mb-2 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-all duration-200" aria-hidden="true"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path></svg><div class="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg transition-all duration-200 group-hover:from-yellow-200/30 group-hover:to-orange-300/30"></div></div><span class="text-white text-sm font-medium text-center drop-shadow-md max-w-20 leading-tight">Interactive Portfolio Website</span></div><div class="absolute flex flex-col items-center cursor-pointer group select-none hover:cursor-grab active:cursor-grabbing" draggable="false" tabindex="0" style="left:165px;top:360px;-webkit-touch-callout:none;-webkit-user-select:none;user-select:none;touch-action:none"><div class="w-16 h-16 mb-2 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-all duration-200" aria-hidden="true"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path></svg><div class="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg transition-all duration-200 group-hover:from-yellow-200/30 group-hover:to-orange-300/30"></div></div><span class="text-white text-sm font-medium text-center drop-shadow-md max-w-20 leading-tight">More Projects</span></div></div><div class="absolute bottom-4 left-1/2 transform -translate-x-1/2"><div class="bg-white/10 backdrop-blur-md rounded-2xl px-3 py-2 border border-white/20 shadow-2xl" style="opacity:0;transform:translateY(100px)"><div class="flex items-end gap-1"><div class="relative flex flex-col items-center"><button class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder w-6 h-6 text-blue-500" aria-hidden="true"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path></svg></button></div><div class="relative flex flex-col items-center"><button class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-monitor w-6 h-6 text-purple-600" aria-hidden="true"><rect width="20" height="14" x="2" y="3" rx="2"></rect><line x1="8" x2="16" y1="21" y2="21"></line><line x1="12" x2="12" y1="17" y2="21"></line></svg></button></div><div class="relative flex flex-col items-center"><button class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calculator w-6 h-6 text-gray-700" aria-hidden="true"><rect width="16" height="20" x="4" y="2" rx="2"></rect><line x1="8" x2="16" y1="6" y2="6"></line><line x1="16" x2="16" y1="14" y2="18"></line><path d="M16 10h.01"></path><path d="M12 10h.01"></path><path d="M8 10h.01"></path><path d="M12 14h.01"></path><path d="M8 14h.01"></path><path d="M12 18h.01"></path><path d="M8 18h.01"></path></svg></button></div><div class="relative flex flex-col items-center"><button class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal w-6 h-6 text-black" aria-hidden="true"><path d="M12 19h8"></path><path d="m4 17 6-6-6-6"></path></svg></button></div><div class="relative flex flex-col items-center"><button class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github w-6 h-6 text-gray-900" aria-hidden="true"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></button></div><div class="relative flex flex-col items-center"><button class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin w-6 h-6 text-blue-600" aria-hidden="true"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg></button></div><div class="relative flex flex-col items-center"><button class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-6 h-6 text-blue-500" aria-hidden="true"><path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"></path><rect x="2" y="4" width="20" height="16" rx="2"></rect></svg></button></div></div></div></div></div></div></div><div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mt-12" style="opacity:0;transform:translateY(30px)"><div class="text-center p-6 bg-card/50 border border-border/50 rounded-lg"><div class="text-2xl lg:text-3xl font-bold text-primary mb-2">10+</div><div class="text-sm text-muted-foreground">Projects Completed</div></div><div class="text-center p-6 bg-card/50 border border-border/50 rounded-lg"><div class="text-2xl lg:text-3xl font-bold text-primary mb-2">15+</div><div class="text-sm text-muted-foreground">Technologies Used</div></div><div class="text-center p-6 bg-card/50 border border-border/50 rounded-lg"><div class="text-2xl lg:text-3xl font-bold text-primary mb-2">100%</div><div class="text-sm text-muted-foreground">Client Satisfaction</div></div><div class="text-center p-6 bg-card/50 border border-border/50 rounded-lg"><div class="text-2xl lg:text-3xl font-bold text-primary mb-2">2+</div><div class="text-sm text-muted-foreground">Years Experience</div></div></div></div></div></section><section id="experience" class="min-h-screen py-20 bg-background relative overflow-hidden"><div class="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/3 to-transparent"></div><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"><div style="opacity:0"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-3xl sm:text-4xl font-bold text-foreground mb-4">Experience &amp; Certifications</h2><div class="w-20 h-1 bg-primary mx-auto rounded-full"></div></div><div class="space-y-16"><div style="opacity:0;transform:translateY(30px)"><h3 class="text-2xl font-semibold text-foreground mb-8 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award w-6 h-6 text-primary" aria-hidden="true"><path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"></path><circle cx="12" cy="8" r="6"></circle></svg>Certifications</h3><div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="certification-card group" tabindex="0" style="opacity:0;transform:scale(0.9)"><div class="bg-card/50 border border-border/50 rounded-lg p-6 h-full transition-all duration-300 hover:border-primary/30 hover:shadow-lg"><div class="flex items-start gap-4"><div class="certification-icon flex-shrink-0"><div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award w-6 h-6 text-primary" aria-hidden="true"><path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"></path><circle cx="12" cy="8" r="6"></circle></svg></div></div><div class="certification-details flex-1"><h4 class="font-semibold text-foreground mb-2 leading-tight">Critical Thinking &amp; Problem Solving</h4><p class="text-muted-foreground text-sm">LinkedIn Learning<!-- --> (<!-- -->2023<!-- -->)</p></div></div></div></div><div class="certification-card group" tabindex="0" style="opacity:0;transform:scale(0.9)"><div class="bg-card/50 border border-border/50 rounded-lg p-6 h-full transition-all duration-300 hover:border-primary/30 hover:shadow-lg"><div class="flex items-start gap-4"><div class="certification-icon flex-shrink-0"><div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-6 h-6 text-primary" aria-hidden="true"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg></div></div><div class="certification-details flex-1"><h4 class="font-semibold text-foreground mb-2 leading-tight">Python Programming</h4><p class="text-muted-foreground text-sm">Coursera<!-- --> (<!-- -->2022<!-- -->)</p></div></div></div></div><div class="certification-card group" tabindex="0" style="opacity:0;transform:scale(0.9)"><div class="bg-card/50 border border-border/50 rounded-lg p-6 h-full transition-all duration-300 hover:border-primary/30 hover:shadow-lg"><div class="flex items-start gap-4"><div class="certification-icon flex-shrink-0"><div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-6 h-6 text-primary" aria-hidden="true"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg></div></div><div class="certification-details flex-1"><h4 class="font-semibold text-foreground mb-2 leading-tight">Web Development Fundamentals</h4><p class="text-muted-foreground text-sm">Udemy<!-- --> (<!-- -->2023<!-- -->)</p></div></div></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><h3 class="text-2xl font-semibold text-foreground mb-8 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-6 h-6 text-primary" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg>Extracurricular Activities</h3><div class="space-y-4"><div class="activity-item group" style="opacity:0;transform:scale(0.9)"><div class="flex items-center gap-4 p-4 rounded-lg bg-card/30 border border-border/30 hover:border-primary/30 hover:bg-card/50 transition-all duration-300"><div class="activity-icon flex-shrink-0"><div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-laptop w-5 h-5 text-primary" aria-hidden="true"><path d="M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z"></path><path d="M20.054 15.987H3.946"></path></svg></div></div><span class="text-foreground group-hover:text-foreground/90 transition-colors">Participated in college-level coding competitions (2022-2023)</span></div></div><div class="activity-item group" style="opacity:0;transform:scale(0.9)"><div class="flex items-center gap-4 p-4 rounded-lg bg-card/30 border border-border/30 hover:border-primary/30 hover:bg-card/50 transition-all duration-300"><div class="activity-icon flex-shrink-0"><div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-primary" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg></div></div><span class="text-foreground group-hover:text-foreground/90 transition-colors">Member of the Computer Science Club at NBKRIST College</span></div></div><div class="activity-item group" style="opacity:0;transform:scale(0.9)"><div class="flex items-center gap-4 p-4 rounded-lg bg-card/30 border border-border/30 hover:border-primary/30 hover:bg-card/50 transition-all duration-300"><div class="activity-icon flex-shrink-0"><div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand-heart w-5 h-5 text-primary" aria-hidden="true"><path d="M11 14h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 16"></path><path d="m7 20 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9"></path><path d="m2 15 6 6"></path><path d="M19.5 8.5c.7-.7 1.5-1.6 1.5-2.7A2.73 2.73 0 0 0 16 4a2.78 2.78 0 0 0-5 1.8c0 1.2.8 2 1.5 2.8L16 12Z"></path></svg></div></div><span class="text-foreground group-hover:text-foreground/90 transition-colors">Volunteer for technical events at department symposiums</span></div></div></div></div></div></div></div></section><section id="contact" class="min-h-screen py-20 pb-32 bg-background relative overflow-hidden"><div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/5 via-transparent to-transparent"></div><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"><div style="opacity:0"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6"><span class="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent uppercase tracking-wide">Got a Vision? Let&#x27;s Bring It to Life!</span></h1><p class="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">I&#x27;m enthusiastic about collaborating on innovative projects. Let&#x27;s connect and explore how we can bring your vision to life!</p></div><div class="grid lg:grid-cols-5 gap-12"><div class="lg:col-span-2" style="opacity:0;transform:translateY(30px)"><div class="bg-card/50 border border-border/50 rounded-lg p-8 h-fit"><h3 class="text-xl font-semibold text-foreground mb-6">Contact Information</h3><div class="space-y-6"><a href="mailto:<EMAIL>" class="block hover:scale-105 transition-transform duration-200"><div class="flex items-start gap-4 group"><div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-5 h-5 text-primary" aria-hidden="true"><path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"></path><rect x="2" y="4" width="20" height="16" rx="2"></rect></svg></div><div class="flex-1"><p class="text-sm text-muted-foreground mb-1">Email</p><p class="text-foreground group-hover:text-primary transition-colors"><EMAIL></p></div></div></a><a href="tel:+917989976214" class="block hover:scale-105 transition-transform duration-200"><div class="flex items-start gap-4 group"><div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone w-5 h-5 text-primary" aria-hidden="true"><path d="M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384"></path></svg></div><div class="flex-1"><p class="text-sm text-muted-foreground mb-1">Phone</p><p class="text-foreground group-hover:text-primary transition-colors">+91 ************</p></div></div></a><div class="block"><div class="flex items-start gap-4 group"><div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin w-5 h-5 text-primary" aria-hidden="true"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg></div><div class="flex-1"><p class="text-sm text-muted-foreground mb-1">Location</p><p class="text-foreground group-hover:text-primary transition-colors">Edulapalli(Vi), Gudur(M), Tirupathi(D), Andhra Pradesh, 524409</p></div></div></div></div><div class="mt-8 pt-6 border-t border-border/50"><p class="text-sm text-muted-foreground mb-4">Follow me on</p><div class="flex gap-3"><a href="https://github.com/nrenx" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-muted/50 rounded-lg flex items-center justify-center hover:bg-primary/20 transition-colors" aria-label="GitHub" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github w-5 h-5 text-muted-foreground hover:text-primary transition-colors" aria-hidden="true"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a><a href="https://linkedin.com/in/bollineninarendrachowdary" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-muted/50 rounded-lg flex items-center justify-center hover:bg-primary/20 transition-colors" aria-label="LinkedIn" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin w-5 h-5 text-muted-foreground hover:text-primary transition-colors" aria-hidden="true"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg></a><a href="https://x.com/___CHOWDARY___" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-muted/50 rounded-lg flex items-center justify-center hover:bg-primary/20 transition-colors" aria-label="Twitter" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter w-5 h-5 text-muted-foreground hover:text-primary transition-colors" aria-hidden="true"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg></a></div></div><div class="mt-6"><button class="w-full bg-muted/10 border border-primary/20 text-primary rounded-lg font-medium px-6 py-3 transition-all duration-300 hover:shadow-lg hover:bg-primary hover:text-primary-foreground group" tabindex="0"><span class="flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-4 h-4" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>View Resume</span></button></div></div></div><div class="lg:col-span-3" style="opacity:0;transform:translateY(30px)"><div class="bg-card/50 border border-border/50 rounded-lg p-8"><h3 class="text-xl font-semibold text-foreground mb-6">Send me a message</h3><form class="space-y-6"><div class="grid sm:grid-cols-2 gap-6"><div><label for="name" class="block text-sm font-medium text-foreground mb-2">Name *</label><input type="text" id="name" placeholder="Your Name" class="w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary border-border" name="name" value=""/></div><div><label for="email" class="block text-sm font-medium text-foreground mb-2">Email *</label><input type="email" id="email" placeholder="<EMAIL>" class="w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary border-border" name="email" value=""/></div></div><div><label for="subject" class="block text-sm font-medium text-foreground mb-2">Subject *</label><input type="text" id="subject" placeholder="Subject" class="w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary border-border" name="subject" value=""/></div><div><label for="message" class="block text-sm font-medium text-foreground mb-2">Message *</label><textarea id="message" name="message" rows="6" placeholder="Your message..." class="w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors resize-none focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary border-border"></textarea></div><button type="submit" class="w-full sm:w-auto px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium flex items-center justify-center gap-2 transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed" tabindex="0">Send Message<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send w-4 h-4" aria-hidden="true"><path d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"></path><path d="m21.854 2.147-10.94 10.939"></path></svg></button></form></div></div></div></div></div></section></div></div><!--$--><!--/$--><!--$--><!--/$--></main></div></div><script src="/portfilio/_next/static/chunks/webpack-b26f0683916655f0.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[1654,[\"43\",\"static/chunks/framer-motion-eaab7625ee3d9f1f.js\",\"26\",\"static/chunks/26-7965e5ed4c6ca054.js\",\"177\",\"static/chunks/app/layout-5f7ea02908601492.js\"],\"Layout\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[894,[],\"ClientPageRoot\"]\n6:I[6310,[\"43\",\"static/chunks/framer-motion-eaab7625ee3d9f1f.js\",\"26\",\"static/chunks/26-7965e5ed4c6ca054.js\",\"974\",\"static/chunks/app/page-1175d4c8d8246d06.js\"],\"default\"]\n9:I[9665,[],\"MetadataBoundary\"]\nb:I[9665,[],\"OutletBoundary\"]\ne:I[4911,[],\"AsyncMetadataOutlet\"]\n10:I[9665,[],\"ViewportBoundary\"]\n12:I[6614,[],\"\"]\n:HL[\"/portfilio/_next/static/media/0484562807a97172-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/portfilio/_next/static/media/4c285fdca692ea22-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/portfilio/_next/static/media/8888a3826f4a3af4-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/portfilio/_next/static/media/a34f9d1faa5f3315-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/portfilio/_next/static/media/b957ea75a84b6ea7-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/portfilio/_next/static/media/eafabf029ad39a43-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/portfilio/_next/static/css/f9019c3291a4513c.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"kXM1G66Jk7SKoQoPsEUQt\",\"p\":\"/portfilio\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/portfilio/_next/static/css/f9019c3291a4513c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_dcbc8f __variable_0a793b font-inter antialiased\",\"style\":{\"fontFamily\":\"var(--font-inter), var(--font-poppins), -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif\"},\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"do4FWeuBtdGn_p-VuQ_s2\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$12\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"13:\"$Sreact.suspense\"\n14:I[4911,[],\"AsyncMetadata\"]\n7:{}\n8:{}\na:[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"15:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Portfolio - Full Stack Developer\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Professional portfolio showcasing full-stack development projects, skills, and experience.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Your Name\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"full-stack developer,web development,react,next.js,typescript,portfolio,software engineer\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Your Name\"}],[\"$\",\"meta\",\"5\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"6\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"7\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"Portfolio - Full Stack Developer\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"Professional portfolio showcasing full-stack development projects, skills, and experience.\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:url\",\"content\":\"https://nrenx.github.io/portfilio/\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:site_name\",\"content\":\"Portfolio - Full Stack Developer\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image\",\"content\":\"https://nrenx.github.io/portfilio/assets/images/og-image.jpg\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:alt\",\"content\":\"Portfolio - Full Stack Developer\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:creator\",\"content\":\"@___CHOWDARY___\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:title\",\"content\":\"Portfolio - Full Stack Developer\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:description\",\"content\":\"Professional portfolio showcasing full-stack development projects, skills, and experience.\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:image\",\"content\":\"https://nrenx.github.io/portfilio/assets/images/og-image.jpg\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/portfilio/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"f:{\"metadata\":\"$15:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>