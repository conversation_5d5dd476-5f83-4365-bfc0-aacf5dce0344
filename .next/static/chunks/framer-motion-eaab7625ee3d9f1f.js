"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[43],{760:(t,e,i)=>{i.d(e,{N:()=>y});var n=i(5155),s=i(2115),o=i(869),r=i(2885),a=i(7494),l=i(845),h=i(7351),u=i(1508);class c extends s.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,h.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:i,anchorX:o}=t,r=(0,s.useId)(),a=(0,s.useRef)(null),l=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,s.useContext)(u.Q);return(0,s.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:s,right:u}=l.current;if(i||!a.current||!t||!e)return;a.current.dataset.motionPopId=r;let c=document.createElement("style");return h&&(c.nonce=h),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(r,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===o?"left: ".concat(s):"right: ".concat(u),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[i]),(0,n.jsx)(c,{isPresent:i,childRef:a,sizeRef:l,children:s.cloneElement(e,{ref:a})})}let p=t=>{let{children:e,initial:i,isPresent:o,onExitComplete:a,custom:h,presenceAffectsLayout:u,mode:c,anchorX:p}=t,f=(0,r.M)(m),v=(0,s.useId)(),g=!0,y=(0,s.useMemo)(()=>(g=!1,{id:v,initial:i,isPresent:o,custom:h,onExitComplete:t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;a&&a()},register:t=>(f.set(t,!1),()=>f.delete(t))}),[o,f,a]);return u&&g&&(y={...y}),(0,s.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[o]),s.useEffect(()=>{o||f.size||!a||a()},[o]),"popLayout"===c&&(e=(0,n.jsx)(d,{isPresent:o,anchorX:p,children:e})),(0,n.jsx)(l.t.Provider,{value:y,children:e})};function m(){return new Map}var f=i(2082);let v=t=>t.key||"";function g(t){let e=[];return s.Children.forEach(t,t=>{(0,s.isValidElement)(t)&&e.push(t)}),e}let y=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:h,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:m="left"}=t,[y,x]=(0,f.xQ)(d),P=(0,s.useMemo)(()=>g(e),[e]),S=d&&!y?[]:P.map(v),T=(0,s.useRef)(!0),w=(0,s.useRef)(P),A=(0,r.M)(()=>new Map),[V,C]=(0,s.useState)(P),[E,D]=(0,s.useState)(P);(0,a.E)(()=>{T.current=!1,w.current=P;for(let t=0;t<E.length;t++){let e=v(E[t]);S.includes(e)?A.delete(e):!0!==A.get(e)&&A.set(e,!1)}},[E,S.length,S.join("-")]);let M=[];if(P!==V){let t=[...P];for(let e=0;e<E.length;e++){let i=E[e],n=v(i);S.includes(n)||(t.splice(e,0,i),M.push(i))}return"wait"===c&&M.length&&(t=M),D(g(t)),C(P),null}let{forceRender:b}=(0,s.useContext)(o.L);return(0,n.jsx)(n.Fragment,{children:E.map(t=>{let e=v(t),s=(!d||!!y)&&(P===E||S.includes(e));return(0,n.jsx)(p,{isPresent:s,initial:(!T.current||!!l)&&void 0,custom:i,presenceAffectsLayout:u,mode:c,onExitComplete:s?void 0:()=>{if(!A.has(e))return;A.set(e,!0);let t=!0;A.forEach(e=>{e||(t=!1)}),t&&(null==b||b(),D(w.current),d&&(null==x||x()),h&&h())},anchorX:m,children:t},e)})})}},845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,i)=>{i.d(e,{xQ:()=>o});var n=i(2115),s=i(845);function o(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],e=(0,n.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:r}=e,a=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return r(a)},[t]);let l=(0,n.useCallback)(()=>t&&o&&o(a),[a,o,t]);return!i&&o?[!1,l]:[!0]}},2885:(t,e,i)=>{i.d(e,{M:()=>s});var n=i(2115);function s(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},6001:(t,e,i)=>{function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[o,r]=s(n);e=e(void 0!==i?i:t.custom,o,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[o,r]=s(n);e=e(void 0!==i?i:t.custom,o,r)}return e}function r(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}i.d(e,{P:()=>ns});var a,l,h=i(8777),u=i(9515),c=i(8109),d=i(98);let p=t=>Array.isArray(t);var m=i(3387),f=i(4803);function v(t,e){let i=t.getValue("willChange");if((0,f.S)(i)&&i.add)return i.add(e);if(!i&&m.W.WillChange){let i=new m.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let g=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),y="data-"+g("framerAppearId");var x=i(532),P=i(7823),S=i(7215);let T=t=>null!==t;var w=i(18);let A={type:"spring",stiffness:500,damping:25,restSpeed:10},V=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),C={type:"keyframes",duration:.8},E={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},D=(t,e)=>{let{keyframes:i}=e;return i.length>2?C:w.f.has(t)?t.startsWith("scale")?V(i[1]):A:E},M=function(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;return r=>{let a=(0,h.r)(n,t)||{},l=a.delay||n.delay||0,{elapsed:c=0}=n;c-=(0,S.f)(l);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};!function(t){let{when:e,delay:i,delayChildren:n,staggerChildren:s,staggerDirection:o,repeat:r,repeatType:a,repeatDelay:l,from:h,elapsed:u,...c}=t;return!!Object.keys(c).length}(a)&&Object.assign(d,D(t,d)),d.duration&&(d.duration=(0,S.f)(d.duration)),d.repeatDelay&&(d.repeatDelay=(0,S.f)(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(p=!0)),(m.W.instantAnimations||m.W.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,p&&!o&&void 0!==e.get()){let t=function(t,e,i){let{repeat:n,repeatType:s="loop"}=e,o=t.filter(T),r=n&&"loop"!==s&&n%2==1?0:o.length-1;return o[r]}(d.keyframes,a);if(void 0!==t)return void u.Gt.update(()=>{d.onUpdate(t),d.onComplete()})}return a.isSync?new x.s(d):new P.A(d)}};function b(t,e){let{delay:i=0,transitionOverride:n,type:s}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(o=n);let m=[],f=s&&t.animationState&&t.animationState.getState()[s];for(let e in l){var g;let n=t.getValue(e,null!=(g=t.latestValues[e])?g:null),s=l[e];if(void 0===s||f&&function(t,e){let{protectedKeys:i,needsAnimating:n}=t,s=i.hasOwnProperty(e)&&!0!==n[e];return n[e]=!1,s}(f,e))continue;let r={delay:i,...(0,h.r)(o||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(s)&&s===a&&!r.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[y];if(i){let t=window.MotionHandoffAnimation(i,e,u.Gt);null!==t&&(r.startTime=t,d=!0)}}v(t,e),n.start(M(e,n,s,t.shouldReduceMotion&&c.$.has(e)?{type:!1}:r,t,d));let p=n.animation;p&&m.push(p)}return a&&Promise.all(m).then(()=>{u.Gt.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=r(t,e)||{};for(let e in s={...s,...i}){var o;let i=p(o=s[e])?o[o.length-1]||0:o;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,d.OQ)(i))}}(t,a)})}),m}function R(t,e){var i;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=r(t,e,"exit"===n.type?null==(i=t.presenceContext)?void 0:i.custom:void 0),{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);let a=s?()=>Promise.all(b(t,s,n)):()=>Promise.resolve(),l=t.variantChildren&&t.variantChildren.size?function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=arguments.length>5?arguments[5]:void 0,r=[],a=(t.variantChildren.size-1)*n,l=1===s?function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return t*n}:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return a-t*n};return Array.from(t.variantChildren).sort(j).forEach((t,n)=>{t.notify("AnimationStart",e),r.push(R(t,e,{...o,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:h}=o;if(!h)return Promise.all([a(),l(n.delay)]);{let[t,e]="beforeChildren"===h?[a,l]:[l,a];return t().then(()=>e())}}function j(t,e){return t.sortNodePosition(e)}function k(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function L(t){return"string"==typeof t||Array.isArray(t)}let B=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],F=["initial",...B],U=F.length,O=[...B].reverse(),I=B.length;function W(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function N(){return{animate:W(!0),whileInView:W(),whileHover:W(),whileTap:W(),whileDrag:W(),whileFocus:W(),exit:W()}}class G{update(){}constructor(t){this.isMounted=!1,this.node=t}}class H extends G{updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(e=>{let{animation:i,options:n}=e;return function(t,e){let i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>R(t,e,n)));else if("string"==typeof e)i=R(t,e,n);else{let s="function"==typeof e?r(t,e,n.custom):e;i=Promise.all(b(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,i,n)})),i=N(),s=!0,o=e=>(i,n)=>{var s;let o=r(t,n,"exit"===e?null==(s=t.presenceContext)?void 0:s.custom:void 0);if(o){let{transition:t,transitionEnd:e,...n}=o;i={...i,...n,...e}}return i};function a(a){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<U;t++){let n=F[t],s=e.props[n];(L(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},u=[],c=new Set,d={},m=1/0;for(let e=0;e<I;e++){var f,v;let r=O[e],g=i[r],y=void 0!==l[r]?l[r]:h[r],x=L(y),P=r===a?g.isActive:null;!1===P&&(m=e);let S=y===h[r]&&y!==l[r]&&x;if(S&&s&&t.manuallyAnimateOnMount&&(S=!1),g.protectedKeys={...d},!g.isActive&&null===P||!y&&!g.prevProp||n(y)||"boolean"==typeof y)continue;let T=(f=g.prevProp,"string"==typeof(v=y)?v!==f:!!Array.isArray(v)&&!k(v,f)),w=T||r===a&&g.isActive&&!S&&x||e>m&&x,A=!1,V=Array.isArray(y)?y:[y],C=V.reduce(o(r),{});!1===P&&(C={});let{prevResolvedValues:E={}}=g,D={...E,...C},M=e=>{w=!0,c.has(e)&&(A=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in D){let e=C[t],i=E[t];if(d.hasOwnProperty(t))continue;let n=!1;(p(e)&&p(i)?k(e,i):e===i)?void 0!==e&&c.has(t)?M(t):g.protectedKeys[t]=!0:null!=e?M(t):c.add(t)}g.prevProp=y,g.prevResolvedValues=C,g.isActive&&(d={...d,...C}),s&&t.blockInitialAnimation&&(w=!1);let b=!(S&&T)||A;w&&b&&u.push(...V.map(t=>({animation:t,options:{type:r}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=r(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=null!=n?n:null}),u.push({animation:e})}let g=!!u.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(u):Promise.resolve()}return{animateChanges:a,setActive:function(e,n){var s;if(i[e].isActive===n)return Promise.resolve();null==(s=t.variantChildren)||s.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,n)}),i[e].isActive=n;let o=a(e);for(let t in i)i[t].protectedKeys={};return o},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=N(),s=!0}}}(t))}}let z=0;class Q extends G{update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}constructor(){super(...arguments),this.id=z++}}var X=i(9827),q=i(3147),Y=i(4158),K=i(3210),_=i(4542);function Z(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}var $=i(2960);function J(t){return{point:{x:t.pageX,y:t.pageY}}}let tt=t=>e=>(0,$.M)(e)&&t(e,J(e));function te(t,e,i,n){return Z(t,e,tt(i),n)}function ti(t){let{top:e,left:i,right:n,bottom:s}=t;return{x:{min:i,max:n},y:{min:e,max:s}}}function tn(t){return t.max-t.min}function ts(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;t.origin=n,t.originPoint=(0,K.k)(e.min,e.max,t.origin),t.scale=tn(i)/tn(e),t.translate=(0,K.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function to(t,e,i,n){ts(t.x,e.x,i.x,n?n.originX:void 0),ts(t.y,e.y,i.y,n?n.originY:void 0)}function tr(t,e,i){t.min=i.min+e.min,t.max=t.min+tn(e)}function ta(t,e,i){t.min=e.min-i.min,t.max=t.min+tn(e)}function tl(t,e,i){ta(t.x,e.x,i.x),ta(t.y,e.y,i.y)}let th=()=>({translate:0,scale:1,origin:0,originPoint:0}),tu=()=>({x:th(),y:th()}),tc=()=>({min:0,max:0}),td=()=>({x:tc(),y:tc()});function tp(t){return[t("x"),t("y")]}function tm(t){return void 0===t||1===t}function tf(t){let{scale:e,scaleX:i,scaleY:n}=t;return!tm(e)||!tm(i)||!tm(n)}function tv(t){return tf(t)||tg(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function tg(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function ty(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function tx(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0;t.min=ty(t.min,e,i,n,s),t.max=ty(t.max,e,i,n,s)}function tP(t,e){let{x:i,y:n}=e;tx(t.x,i.translate,i.scale,i.originPoint),tx(t.y,n.translate,n.scale,n.originPoint)}function tS(t,e){t.min=t.min+e,t.max=t.max+e}function tT(t,e,i,n){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,o=(0,K.k)(t.min,t.max,s);tx(t,e,i,o,n)}function tw(t,e){tT(t.x,e.x,e.scaleX,e.scale,e.originX),tT(t.y,e.y,e.scaleY,e.scale,e.originY)}function tA(t,e){return ti(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let tV=t=>{let{current:e}=t;return e?e.ownerDocument.defaultView:null};function tC(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var tE=i(3191);let tD=(t,e)=>Math.abs(t-e);class tM{updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,u.WG)(this.updatePoint)}constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=tj(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(tD(t.x,e.x)**2+tD(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=u.uv;this.history.push({...n,timestamp:s});let{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=tb(e,this.transformPagePoint),u.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=tj("pointercancel"===t.type?this.lastMoveEventInfo:tb(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,o),n&&n(t,o)},!(0,$.M)(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let o=tb(J(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=u.uv;this.history=[{...r,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,tj(o,this.history)),this.removeListeners=(0,tE.F)(te(this.contextWindow,"pointermove",this.handlePointerMove),te(this.contextWindow,"pointerup",this.handlePointerUp),te(this.contextWindow,"pointercancel",this.handlePointerUp))}}function tb(t,e){return e?{point:e(t.point)}:t}function tR(t,e){return{x:t.x-e.x,y:t.y-e.y}}function tj(t,e){let{point:i}=t;return{point:i,delta:tR(i,tk(e)),offset:tR(i,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=tk(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>(0,S.f)(.1)));)i--;if(!n)return{x:0,y:0};let o=(0,S.X)(s.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let r={x:(s.x-n.x)/o,y:(s.y-n.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}(e,.1)}}function tk(t){return t[t.length-1]}var tL=i(5818),tB=i(1297);function tF(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function tU(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function tO(t,e,i){return{min:tI(t,e),max:tI(t,i)}}function tI(t,e){return"number"==typeof t?t:t[e]||0}let tW=new WeakMap;class tN{start(t){let{snapToCursor:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new tM(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(J(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,q.W)(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),tp(t=>{let e=this.getAxisMotionValue(t).get()||0;if(Y.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=tn(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&u.Gt.postRender(()=>s(t,e)),v(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:o}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:r}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(r),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>tp(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:tV(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:s}=this.getProps();s&&u.Gt.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!tG(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),o=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(o=function(t,e,i){let{min:n,max:s}=e;return void 0!==n&&t<n?t=i?(0,K.k)(n,t,i.min):Math.max(t,n):void 0!==s&&t>s&&(t=i?(0,K.k)(s,t,i.max):Math.min(t,s)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,s=this.constraints;e&&tC(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,e){let{top:i,left:n,bottom:s,right:o}=e;return{x:tF(t.x,n,o),y:tF(t.y,i,s)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===t?t=0:!0===t&&(t=.35),{x:tO(t,"left","right"),y:tO(t,"top","bottom")}}(i),s!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&tp(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!tC(e))return!1;let n=e.current;(0,_.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let o=function(t,e,i){let n=tA(t,i),{scroll:s}=e;return s&&(tS(n.x,s.offset.x),tS(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),r=(t=s.layout.layoutBox,{x:tU(t.x,o.x),y:tU(t.y,o.y)});if(i){let t=i(function(t){let{x:e,y:i}=t;return{top:i.min,right:e.max,bottom:i.max,left:e.min}}(r));this.hasMutatedConstraints=!!t,t&&(r=ti(t))}return r}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{};return Promise.all(tp(r=>{if(!tG(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[r]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)})).then(r)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return v(this.visualElement,t),i.start(M(t,i,0,e,this.visualElement,!1))}stopAnimation(){tp(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){tp(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e="_drag".concat(t.toUpperCase()),i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){tp(e=>{let{drag:i}=this.getProps();if(!tG(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:o}=n.layout.layoutBox[e];s.set(t[e]-(0,K.k)(i,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!tC(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};tp(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=tn(t),s=tn(e);return s>n?i=(0,tL.q)(e.min,e.max-n,t.min):n>s&&(i=(0,tL.q)(t.min,t.max-s,e.min)),(0,tB.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),tp(e=>{if(!tG(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];i.set((0,K.k)(s,o,n[e]))})}addListeners(){if(!this.visualElement.current)return;tW.set(this.visualElement,this);let t=te(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();tC(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),u.Gt.read(e);let s=Z(window,"resize",()=>this.scalePositionWithinConstraints()),o=i.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i}=t;this.isDragging&&i&&(tp(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:o=.35,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:o,dragMomentum:r}}constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=td(),this.visualElement=t}}function tG(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class tH extends G{mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||X.l}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(t){super(t),this.removeGroupControls=X.l,this.removeListeners=X.l,this.controls=new tN(t)}}let tz=t=>(e,i)=>{t&&u.Gt.postRender(()=>t(e,i))};class tQ extends G{onPointerDown(t){this.session=new tM(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:tV(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:tz(t),onStart:tz(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&u.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=te(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=X.l}}var tX=i(5155),tq=i(7123),tY=i(2115),tK=i(2082),t_=i(869);let tZ=(0,tY.createContext)({}),t$={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tJ(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let t0={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!Y.px.test(t))return t;else t=parseFloat(t);let i=tJ(t,e.target.x),n=tJ(t,e.target.y);return"".concat(i,"% ").concat(n,"%")}};var t1=i(10),t2=i(8606);let t5={};class t3 extends tY.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;for(let t in t4)t5[t]=t4[t],(0,t2.j)(t)&&(t5[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),t$.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:o}=i;return o&&(o.isPresent=s,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||u.Gt.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),tq.k.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function t9(t){let[e,i]=(0,tK.xQ)(),n=(0,tY.useContext)(t_.L);return(0,tX.jsx)(t3,{...t,layoutGroup:n,switchLayoutGroup:(0,tY.useContext)(tZ),isPresent:e,safeToRemove:i})}let t4={borderRadius:{...t0,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:t0,borderTopRightRadius:t0,borderBottomLeftRadius:t0,borderBottomRightRadius:t0,boxShadow:{correct:(t,e)=>{let{treeScale:i,projectionDelta:n}=e,s=t1.f.parse(t);if(s.length>5)return t;let o=t1.f.createTransformer(t),r=+("number"!=typeof s[0]),a=n.x.scale*i.x,l=n.y.scale*i.y;s[0+r]/=a,s[1+r]/=l;let h=(0,K.k)(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=h),"number"==typeof s[3+r]&&(s[3+r]/=h),o(s)}}};var t8=i(9782),t7=i(5943),t6=i(4261),et=i(4744),ee=i(3704),ei=i(5626),en=i(6668);let es=(t,e)=>t.depth-e.depth;class eo{add(t){(0,en.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,en.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(es),this.isDirty=!1,this.children.forEach(t)}constructor(){this.children=[],this.isDirty=!1}}function er(t){return(0,f.S)(t)?t.get():t}var ea=i(7712);let el=["TopLeft","TopRight","BottomLeft","BottomRight"],eh=el.length,eu=t=>"string"==typeof t?parseFloat(t):t,ec=t=>"number"==typeof t||Y.px.test(t);function ed(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let ep=ef(0,.5,ea.yT),em=ef(.5,.95,X.l);function ef(t,e,i){return n=>n<t?0:n>e?1:i((0,tL.q)(t,e,n))}function ev(t,e){t.min=e.min,t.max=e.max}function eg(t,e){ev(t.x,e.x),ev(t.y,e.y)}function ey(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ex(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function eP(t,e,i,n,s){let[o,r,a]=i;!function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,s=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:t,r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:t;if(Y.KN.test(e)&&(e=parseFloat(e),e=(0,K.k)(r.min,r.max,e/100)-r.min),"number"!=typeof e)return;let a=(0,K.k)(o.min,o.max,n);t===o&&(a-=e),t.min=ex(t.min,e,i,a,s),t.max=ex(t.max,e,i,a,s)}(t,e[o],e[r],e[a],e.scale,n,s)}let eS=["x","scaleX","originX"],eT=["y","scaleY","originY"];function ew(t,e,i,n){eP(t.x,e,eS,i?i.x:void 0,n?n.x:void 0),eP(t.y,e,eT,i?i.y:void 0,n?n.y:void 0)}function eA(t){return 0===t.translate&&1===t.scale}function eV(t){return eA(t.x)&&eA(t.y)}function eC(t,e){return t.min===e.min&&t.max===e.max}function eE(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function eD(t,e){return eE(t.x,e.x)&&eE(t.y,e.y)}function eM(t){return tn(t.x)/tn(t.y)}function eb(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class eR{add(t){(0,en.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,en.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let ej={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ek=["","X","Y","Z"],eL={visibility:"hidden"},eB=0;function eF(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function eU(t){let{attachResizeListener:e,defaultParent:i,measureScroll:n,checkIsScrollRoot:s,resetTransform:o}=t;return class{addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ei.v),this.eventHandlers.get(t).add(e)}notifyListeners(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];let s=this.eventHandlers.get(t);s&&s.notify(...i)}hasListeners(t){return this.eventHandlers.has(t)}mount(t){if(this.instance)return;this.isSVG=(0,t8.x)(t)&&!(0,t7.h)(t),this.instance=t;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),e){let i,n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=t6.k.now(),n=s=>{let{timestamp:o}=s,r=o-i;r>=250&&((0,u.WG)(n),t(r-e))};return u.Gt.setup(n,!0),()=>(0,u.WG)(n)}(n,250),t$.hasAnimatedSinceResize&&(t$.hasAnimatedSinceResize=!1,this.nodes.forEach(eX))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i,hasRelativeLayoutChanged:n,layout:o}=t;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||e0,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=s.getProps(),u=!this.targetLayout||!eD(this.targetLayout,o),c=!i&&n;if(this.options.layoutRoot||this.resumeFrom||c||i&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...(0,h.r)(r,"layout"),onPlay:a,onComplete:l};(s.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else i||eX(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=o})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,u.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eK),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[y];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",u.Gt,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eH);return}this.isUpdating||this.nodes.forEach(ez),this.isUpdating=!1,this.nodes.forEach(eQ),this.nodes.forEach(eO),this.nodes.forEach(eI),this.clearAllSnapshots();let t=t6.k.now();u.uv.delta=(0,tB.q)(0,1e3/60,t-u.uv.timestamp),u.uv.timestamp=t,u.uv.isProcessing=!0,u.PP.update.process(u.uv),u.PP.preRender.process(u.uv),u.PP.render.process(u.uv),u.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tq.k.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eG),this.sharedNodes.forEach(e_)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,u.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){u.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||tn(this.snapshot.measuredBox.x)||tn(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=td(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!o)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!eV(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||tv(this.latestValues)||s)&&(o(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=this.measurePageBox(),n=this.removeElementScroll(i);return e&&(n=this.removeTransform(n)),e5((t=n).x),e5(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return td();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(e9))){let{scroll:t}=this.root;t&&(tS(i.x,t.offset.x),tS(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=td();if(eg(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:s,options:o}=n;n!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&eg(i,t),tS(i.x,s.offset.x),tS(i.y,s.offset.y))}return i}applyTransform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=td();eg(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&tw(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),tv(n.latestValues)&&tw(i,n.latestValues)}return tv(this.latestValues)&&tw(i,this.latestValues),i}removeTransform(t){let e=td();eg(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!tv(i.latestValues))continue;tf(i.latestValues)&&i.updateSnapshot();let n=td();eg(n,i.measurePageBox()),ew(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return tv(this.latestValues)&&ew(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==u.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var t,e,i,n;let s=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==o;if(!(s||r&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=u.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=td(),this.relativeTargetOrigin=td(),tl(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),eg(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=td(),this.targetWithTransforms=td()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),e=this.target,i=this.relativeTarget,n=this.relativeParent.target,tr(e.x,i.x,n.x),tr(e.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):eg(this.target,this.layout.layoutBox),tP(this.target,this.targetDelta)):eg(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=td(),this.relativeTargetOrigin=td(),tl(this.relativeTargetOrigin,this.target,t.target),eg(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}et.Q.value&&ej.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||tf(this.parent.latestValues)||tg(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===u.uv.timestamp&&(n=!1),n)return;let{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||o))return;eg(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i){let n,s,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=i.length;if(r){e.x=e.y=1;for(let a=0;a<r;a++){s=(n=i[a]).projectionDelta;let{visualElement:r}=n.options;(!r||!r.props.style||"contents"!==r.props.style.display)&&(o&&n.options.layoutScroll&&n.scroll&&n!==n.root&&tw(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,tP(t,s)),o&&tv(n.latestValues)&&tw(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=td());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ey(this.prevProjectionDelta.x,this.projectionDelta.x),ey(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),to(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&eb(this.projectionDelta.x,this.prevProjectionDelta.x)&&eb(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),et.Q.value&&ej.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tu(),this.projectionDelta=tu(),this.projectionDeltaWithTransform=tu()}setAnimationOrigin(t){let e,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.snapshot,s=n?n.latestValues:{},o={...this.latestValues},r=tu();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;let a=td(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,c=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(eJ));this.animationProgress=0,this.mixTargetDelta=i=>{let n=i/1e3;if(eZ(r.x,t.x,n),eZ(r.y,t.y,n),this.setTargetDelta(r),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,d,p,m,f,v;tl(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=n,e$(p.x,m.x,f.x,v),e$(p.y,m.y,f.y,v),e&&(h=this.relativeTarget,d=e,eC(h.x,d.x)&&eC(h.y,d.y))&&(this.isProjectionDirty=!1),e||(e=td()),eg(e,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,i,n,s,o){var r,a,l,h;s?(t.opacity=(0,K.k)(0,null!=(r=i.opacity)?r:1,ep(n)),t.opacityExit=(0,K.k)(null!=(a=e.opacity)?a:1,0,em(n))):o&&(t.opacity=(0,K.k)(null!=(l=e.opacity)?l:1,null!=(h=i.opacity)?h:1,n));for(let s=0;s<eh;s++){let o="border".concat(el[s],"Radius"),r=ed(e,o),a=ed(i,o);(void 0!==r||void 0!==a)&&(r||(r=0),a||(a=0),0===r||0===a||ec(r)===ec(a)?(t[o]=Math.max((0,K.k)(eu(r),eu(a),n),0),(Y.KN.test(a)||Y.KN.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,K.k)(e.rotate||0,i.rotate||0,n))}(o,s,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){var e,i,n;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(),null==(n=this.resumingFrom)||null==(i=n.currentAnimation)||i.stop(),this.pendingAnimation&&((0,u.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=u.Gt.update(()=>{t$.hasAnimatedSinceResize=!0,ee.q.layout++,this.motionValue||(this.motionValue=(0,d.OQ)(0)),this.currentAnimation=function(t,e,i){let n=(0,f.S)(t)?t:(0,d.OQ)(t);return n.start(M("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ee.q.layout--},onComplete:()=>{ee.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&e3(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||td();let e=tn(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=tn(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}eg(e,i),tw(e,s),to(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new eR),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote(){let{needsReset:t,transition:e,preserveFollowOpacity:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&eF("z",t,n,this.animationValues);for(let e=0;e<ek.length;e++)eF("rotate".concat(ek[e]),t,n,this.animationValues),eF("skew".concat(ek[e]),t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return eL;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=er(null==t?void 0:t.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=er(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!tv(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let s=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==i?void 0:i.z)||0;if((s||o||r)&&(n="translate3d(".concat(s,"px, ").concat(o,"px, ").concat(r,"px) ")),(1!==e.x||1!==e.y)&&(n+="scale(".concat(1/e.x,", ").concat(1/e.y,") ")),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=i;t&&(n="perspective(".concat(t,"px) ").concat(n)),e&&(n+="rotate(".concat(e,"deg) ")),s&&(n+="rotateX(".concat(s,"deg) ")),o&&(n+="rotateY(".concat(o,"deg) ")),r&&(n+="skewX(".concat(r,"deg) ")),a&&(n+="skewY(".concat(a,"deg) "))}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+="scale(".concat(a,", ").concat(l,")")),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(e.transform=i(s,e.transform));let{x:o,y:r}=this.projectionDelta;if(e.transformOrigin="".concat(100*o.origin,"% ").concat(100*r.origin,"% 0"),n.animationValues){var a,l;e.opacity=n===this?null!=(l=null!=(a=s.opacity)?a:this.latestValues.opacity)?l:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit}else e.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(let t in t5){if(void 0===s[t])continue;let{correct:i,applyTo:o,isCSSVariable:r}=t5[t],a="none"===e.transform?s[t]:i(s[t],n);if(o){let t=o.length;for(let i=0;i<t;i++)e[o[i]]=a}else r?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?er(null==t?void 0:t.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(eH),this.root.sharedNodes.clear()}constructor(t={},e=null==i?void 0:i()){this.id=eB++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,et.Q.value&&(ej.nodes=ej.calculatedTargetDeltas=ej.calculatedProjections=0),this.nodes.forEach(eW),this.nodes.forEach(eq),this.nodes.forEach(eY),this.nodes.forEach(eN),et.Q.addProjectionMetrics&&et.Q.addProjectionMetrics(ej)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=e?e.root||e:this,this.path=e?[...e.path,e]:[],this.parent=e,this.depth=e?e.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new eo)}}}function eO(t){t.updateLayout()}function eI(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:s}=t.options,o=i.source!==t.layout.source;"size"===s?tp(t=>{let n=o?i.measuredBox[t]:i.layoutBox[t],s=tn(n);n.min=e[t].min,n.max=n.min+s}):e3(s,i.layoutBox,e)&&tp(n=>{let s=o?i.measuredBox[n]:i.layoutBox[n],r=tn(e[n]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+r)});let r=tu();to(r,e,i.layoutBox);let a=tu();o?to(a,t.applyTransform(n,!0),i.measuredBox):to(a,e,i.layoutBox);let l=!eV(r),h=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:o}=n;if(s&&o){let r=td();tl(r,i.layoutBox,s.layoutBox);let a=td();tl(a,e,o.layoutBox),eD(r,a)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function eW(t){et.Q.value&&ej.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function eN(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function eG(t){t.clearSnapshot()}function eH(t){t.clearMeasurements()}function ez(t){t.isLayoutDirty=!1}function eQ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function eX(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function eq(t){t.resolveTargetDelta()}function eY(t){t.calcProjection()}function eK(t){t.resetSkewAndRotation()}function e_(t){t.removeLeadSnapshot()}function eZ(t,e,i){t.translate=(0,K.k)(e.translate,0,i),t.scale=(0,K.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function e$(t,e,i,n){t.min=(0,K.k)(e.min,i.min,n),t.max=(0,K.k)(e.max,i.max,n)}function eJ(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let e0={duration:.45,ease:[.4,0,.1,1]},e1=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),e2=e1("applewebkit/")&&!e1("chrome/")?Math.round:X.l;function e5(t){t.min=e2(t.min),t.max=e2(t.max)}function e3(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(eM(e)-eM(i)))}function e9(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let e4=eU({attachResizeListener:(t,e)=>Z(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),e8={current:void 0},e7=eU({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!e8.current){let t=new e4({});t.mount(window),t.setOptions({layoutScroll:!0}),e8.current=t}return e8.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var e6=i(6121);function it(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&u.Gt.postRender(()=>s(e,J(e)))}class ie extends G{mount(){let{current:t}=this.node;t&&(this.unmount=(0,e6.P)(t,(t,e)=>(it(this.node,e,"Start"),t=>it(this.node,t,"End"))))}unmount(){}}class ii extends G{onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,tE.F)(Z(this.node.current,"focus",()=>this.onFocus()),Z(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}var is=i(5273);function io(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&u.Gt.postRender(()=>s(e,J(e)))}class ir extends G{mount(){let{current:t}=this.node;t&&(this.unmount=(0,is.c)(t,(t,e)=>(io(this.node,e,"Start"),(t,e)=>{let{success:i}=e;return io(this.node,t,i?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ia=new WeakMap,il=new WeakMap,ih=t=>{let e=ia.get(t.target);e&&e(t)},iu=t=>{t.forEach(ih)},ic={some:0,all:1};class id extends G{startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:ic[n]};return function(t,e,i){let n=function(t){let{root:e,...i}=t,n=e||document;il.has(n)||il.set(n,{});let s=il.get(n),o=JSON.stringify(i);return s[o]||(s[o]=new IntersectionObserver(iu,{root:e,...i})),s[o]}(e);return ia.set(t,i),n.observe(t),()=>{ia.delete(t),n.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),o=e?i:n;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function(t){let{viewport:e={}}=t,{viewport:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=>e[t]!==i[t]}(t,e))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}let ip=(0,tY.createContext)({strict:!1});var im=i(1508);let iv=(0,tY.createContext)({});function ig(t){return n(t.animate)||F.some(e=>L(t[e]))}function iy(t){return!!(ig(t)||t.variants)}function ix(t){return Array.isArray(t)?t.join(" "):t}var iP=i(8972);let iS={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iT={};for(let t in iS)iT[t]={isEnabled:e=>iS[t].some(t=>!!e[t])};let iw=Symbol.for("motionComponentSymbol");var iA=i(845),iV=i(7494);function iC(t,e){let{layout:i,layoutId:n}=e;return w.f.has(t)||t.startsWith("origin")||(i||void 0!==n)&&(!!t5[t]||"opacity"===t)}var iE=i(5708),iD=i(2403);let iM={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ib=w.U.length;function iR(t,e,i){let{style:n,vars:s,transformOrigin:o}=t,r=!1,a=!1;for(let t in e){let i=e[t];if(w.f.has(t)){r=!0;continue}if((0,t2.j)(t)){s[t]=i;continue}{let e=(0,iE.e)(i,iD.W[t]);t.startsWith("origin")?(a=!0,o[t]=e):n[t]=e}}if(!e.transform&&(r||i?n.transform=function(t,e,i){let n="",s=!0;for(let o=0;o<ib;o++){let r=w.U[o],a=t[r];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!r.startsWith("scale"):0===parseFloat(a))||i){let t=(0,iE.e)(a,iD.W[r]);if(!l){s=!1;let e=iM[r]||r;n+="".concat(e,"(").concat(t,") ")}i&&(e[r]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=o;n.transformOrigin="".concat(t," ").concat(e," ").concat(i)}}let ij=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ik(t,e,i){for(let n in e)(0,f.S)(e[n])||iC(n,i)||(t[n]=e[n])}let iL={offset:"stroke-dashoffset",array:"stroke-dasharray"},iB={offset:"strokeDashoffset",array:"strokeDasharray"};function iF(t,e,i,n,s){var o,r;let{attrX:a,attrY:l,attrScale:h,pathLength:u,pathSpacing:c=1,pathOffset:d=0,...p}=e;if(iR(t,p,n),i){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:m,style:f}=t;m.transform&&(f.transform=m.transform,delete m.transform),(f.transform||m.transformOrigin)&&(f.transformOrigin=null!=(o=m.transformOrigin)?o:"50% 50%",delete m.transformOrigin),f.transform&&(f.transformBox=null!=(r=null==s?void 0:s.transformBox)?r:"fill-box",delete m.transformBox),void 0!==a&&(m.x=a),void 0!==l&&(m.y=l),void 0!==h&&(m.scale=h),void 0!==u&&function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=!(arguments.length>4)||void 0===arguments[4]||arguments[4];t.pathLength=1;let o=s?iL:iB;t[o.offset]=Y.px.transform(-n);let r=Y.px.transform(e),a=Y.px.transform(i);t[o.array]="".concat(r," ").concat(a)}(m,u,c,d,!1)}let iU=()=>({...ij(),attrs:{}}),iO=t=>"string"==typeof t&&"svg"===t.toLowerCase(),iI=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function iW(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||iI.has(t)}let iN=t=>!iW(t);try{!function(t){t&&(iN=e=>e.startsWith("on")?!iW(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let iG=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iH(t){if("string"!=typeof t||t.includes("-"));else if(iG.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var iz=i(2885);let iQ=t=>(e,i)=>{let s=(0,tY.useContext)(iv),r=(0,tY.useContext)(iA.t),a=()=>(function(t,e,i,s){let{scrapeMotionValuesFromProps:r,createRenderState:a}=t;return{latestValues:function(t,e,i,s){let r={},a=s(t,{});for(let t in a)r[t]=er(a[t]);let{initial:l,animate:h}=t,u=ig(t),c=iy(t);e&&c&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(e,i,s,r),renderState:a()}})(t,e,s,r);return i?a():(0,iz.M)(a)};function iX(t,e,i){let{style:n}=t,s={};for(let r in n){var o;((0,f.S)(n[r])||e.style&&(0,f.S)(e.style[r])||iC(r,t)||(null==i||null==(o=i.getValue(r))?void 0:o.liveStyle)!==void 0)&&(s[r]=n[r])}return s}let iq={useVisualState:iQ({scrapeMotionValuesFromProps:iX,createRenderState:ij})};function iY(t,e,i){let n=iX(t,e,i);for(let i in t)((0,f.S)(t[i])||(0,f.S)(e[i]))&&(n[-1!==w.U.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let iK={useVisualState:iQ({scrapeMotionValuesFromProps:iY,createRenderState:iU})};var i_=i(4160),iZ=i(8037),i$=i(8557),iJ=i(7277),i0=i(7322),i1=i(3014),i2=i(7312);let i5={current:null},i3={current:!1},i9=new WeakMap,i4=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class i8{scrapeMotionValuesFromProps(t,e,i){return{}}mount(t){this.current=t,i9.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),i3.current||function(){if(i3.current=!0,iP.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>i5.current=t.matches;t.addListener(e),e()}else i5.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||i5.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,u.WG)(this.notifyUpdate),(0,u.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=w.f.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&u.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),o(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in iT){let e=iT[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):td()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<i4.length;e++){let i=i4[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],o=i[n];if((0,f.S)(s))t.addValue(n,s);else if((0,f.S)(o))t.addValue(n,(0,d.OQ)(s,{owner:t}));else if(o!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,(0,d.OQ)(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,d.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let n=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&((0,i1.i)(n)||(0,i2.$)(n))?n=parseFloat(n):!(0,i$.t)(n)&&t1.f.test(e)&&(n=(0,iJ.J)(t,e)),this.setBaseTarget(t,(0,f.S)(n)?n.get():n)),(0,f.S)(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){var n;let s=o(this.props,i,null==(n=this.presenceContext)?void 0:n.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||(0,f.S)(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new ei.v),this.events[t].add(e)}notify(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];this.events[t]&&this.events[t].notify(...i)}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=i0.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=t6.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,u.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=r,this.blockInitialAnimation=!!s,this.isControllingVariants=ig(e),this.isVariantNode=iy(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==a[t]&&(0,f.S)(e)&&e.set(a[t],!1)}}}class i7 extends i8{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,e){let{vars:i,style:n}=e;delete i[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,f.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent="".concat(t))}))}constructor(){super(...arguments),this.KeyframeResolver=iZ.K}}function i6(t,e,i,n){let{style:s,vars:o}=e;for(let e in Object.assign(t.style,s,n&&n.getProjectionStyles(i)),o)t.style.setProperty(e,o[e])}class nt extends i7{readValueFromInstance(t,e){var i;if(w.f.has(e))return(null==(i=this.projection)?void 0:i.isProjecting)?(0,i_.zs)(e):(0,i_.Ib)(t,e);{let i=window.getComputedStyle(t),n=((0,t2.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,e){let{transformPagePoint:i}=e;return tA(t,i)}build(t,e,i){iR(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return iX(t,e,i)}constructor(){super(...arguments),this.type="html",this.renderInstance=i6}}var ne=i(1834);let ni=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nn extends i7{getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(w.f.has(e)){let t=(0,ne.D)(e);return t&&t.default||0}return e=ni.has(e)?e:g(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return iY(t,e,i)}build(t,e,i){iF(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in i6(t,e,void 0,n),e.attrs)t.setAttribute(ni.has(i)?i:g(i),e.attrs[i])}mount(t){this.isSVGTag=iO(t.tagName),super.mount(t)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=td}}let ns=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy(function(){for(var e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];return t(...i)},{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((a={animation:{Feature:H},exit:{Feature:Q},inView:{Feature:id},tap:{Feature:ir},focus:{Feature:ii},hover:{Feature:ie},pan:{Feature:tQ},drag:{Feature:tH,ProjectionNode:e7,MeasureLayout:t9},layout:{ProjectionNode:e7,MeasureLayout:t9}},l=(t,e)=>iH(t)?new nn(e):new nt(e,{allowProjection:t!==tY.Fragment}),function(t){let{forwardMotionProps:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{forwardMotionProps:!1};return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:s,useRender:o,useVisualState:r,Component:a}=t;function l(t,e){var i,n,l;let h,u={...(0,tY.useContext)(im.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,tY.useContext)(t_.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=u,d=function(t){let{initial:e,animate:i}=function(t,e){if(ig(t)){let{initial:e,animate:i}=t;return{initial:!1===e||L(e)?e:void 0,animate:L(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,tY.useContext)(iv));return(0,tY.useMemo)(()=>({initial:e,animate:i}),[ix(e),ix(i)])}(t),p=r(t,c);if(!c&&iP.B){n=0,l=0,(0,tY.useContext)(ip).strict;let t=function(t){let{drag:e,layout:i}=iT;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);h=t.MeasureLayout,d.visualElement=function(t,e,i,n,s){var o,r,a,l;let{visualElement:h}=(0,tY.useContext)(iv),u=(0,tY.useContext)(ip),c=(0,tY.useContext)(iA.t),d=(0,tY.useContext)(im.Q).reducedMotion,p=(0,tY.useRef)(null);n=n||u.renderer,!p.current&&n&&(p.current=n(t,{visualState:e,parent:h,props:i,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:d}));let m=p.current,f=(0,tY.useContext)(tZ);m&&!m.projection&&s&&("html"===m.type||"svg"===m.type)&&function(t,e,i,n){let{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:!!r||a&&tC(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:h})}(p.current,i,s,f);let v=(0,tY.useRef)(!1);(0,tY.useInsertionEffect)(()=>{m&&v.current&&m.update(i,c)});let g=i[y],x=(0,tY.useRef)(!!g&&!(null==(o=(r=window).MotionHandoffIsComplete)?void 0:o.call(r,g))&&(null==(a=(l=window).MotionHasOptimisedAnimation)?void 0:a.call(l,g)));return(0,iV.E)(()=>{m&&(v.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),tq.k.render(m.render),x.current&&m.animationState&&m.animationState.animateChanges())}),(0,tY.useEffect)(()=>{m&&(!x.current&&m.animationState&&m.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var t,e;null==(t=(e=window).MotionHandoffMarkAsComplete)||t.call(e,g)}),x.current=!1))}),m}(a,p,u,s,t.ProjectionNode)}return(0,tX.jsxs)(iv.Provider,{value:d,children:[h&&d.visualElement?(0,tX.jsx)(h,{visualElement:d.visualElement,...u}):null,o(a,t,(i=d.visualElement,(0,tY.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):tC(e)&&(e.current=t))},[i])),p,c,d.visualElement)]})}n&&function(t){for(let e in t)iT[e]={...iT[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let h=(0,tY.forwardRef)(l);return h[iw]=a,h}({...iH(t)?iK:iq,preloadedFeatures:a,useRender:function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(e,i,n,s,o)=>{let{latestValues:r}=s,a=(iH(e)?function(t,e,i,n){let s=(0,tY.useMemo)(()=>{let i=iU();return iF(i,e,iO(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ik(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return ik(n,i,t),Object.assign(n,function(t,e){let{transformTemplate:i}=t;return(0,tY.useMemo)(()=>{let t=ij();return iR(t,e,i),Object.assign({},t.vars,t.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":"pan-".concat("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,o,e),l=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(iN(s)||!0===i&&iW(s)||!e&&!iW(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(i,"string"==typeof e,t),h=e!==tY.Fragment?{...l,...a,ref:n}:{},{children:u}=i,c=(0,tY.useMemo)(()=>(0,f.S)(u)?u.get():u,[u]);return(0,tY.createElement)(e,{...h,children:c})}}(e),createVisualElement:l,Component:t})}))},7494:(t,e,i)=>{i.d(e,{E:()=>s});var n=i(2115);let s=i(8972).B?n.useLayoutEffect:n.useEffect},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window}}]);